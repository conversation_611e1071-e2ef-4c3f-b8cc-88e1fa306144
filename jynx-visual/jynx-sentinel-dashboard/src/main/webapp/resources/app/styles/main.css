.browsehappy {
    margin: 0.2em 0;
    background: #ccc;
    color: #000;
    padding: 0.2em 0;
}

body {
    padding: 0;
}

/* Everything but the jumbotron gets side spacing for mobile first views */

.header,
.marketing,
.footer {
    padding-left: 15px;
    padding-right: 15px;
}


/* Custom page header */

.header {
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 10px;
}


/* Make the masthead heading the same height as the navigation */

.header h3 {
    margin-top: 0;
    margin-bottom: 0;
    line-height: 40px;
    padding-bottom: 19px;
}


/* Custom page footer */

.footer {
    padding-top: 19px;
    color: #777;
    border-top: 1px solid #e5e5e5;
}

.container-narrow > hr {
    margin: 30px 0;
}


/* Main marketing message and sign up button */

.jumbotron {
    text-align: center;
    border-bottom: 1px solid #e5e5e5;
}

.jumbotron .btn {
    font-size: 21px;
    padding: 14px 24px;
}


/* Supporting marketing content */

.marketing {
    margin: 40px 0;
}

.marketing p + h4 {
    margin-top: 28px;
}


/* Responsive: Portrait tablets and up */

@media screen and (min-width: 768px) {
    .container {
        width: inherit;
        margin-left: 60px;
        margin-right: 5px;
    }
    /* Remove the padding we set earlier */
    .header,
    .marketing,
    .footer {
        padding-left: 0;
        padding-right: 0;
    }
    /* Space out the masthead */
    .header {
        margin-bottom: 30px;
    }
    /* Remove the bottom border on the jumbotron for visual effect */
    .jumbotron {
        border-bottom: 0;
    }
}

.navbar-inverse {
    background-color: #1d9d74;
    border-color: #1b926c;
}

.navbar-inverse .navbar-nav > li > a {
    color: #b0ddce;
    font-size: 15px;
}

.navbar-inverse .navbar-nav>.open>a,
.navbar-inverse .navbar-nav>.open>a:focus,
.navbar-inverse .navbar-nav>.open>a:hover {
    background-color: #1b926c;
}

@media (min-width: 900px) {
    .navbar-left {
        float: left !important;
    }
    .navbar-right {
        float: right !important;
        margin-right: 0%;
    }
    .navbar-right ~ .navbar-right {
        margin-right: 0;
    }
}

.dropdown-menu {
    min-width: 100px !important;
}

.nav-sidebar li.active a {
    background: #DDD;
}

.dropdown-menu>li>a:hover, .dropdown-menu>li>a:focus {
    background: #1d9d74;
    /*background: #d9d9d9;*/
    color: white;
}

.broadcast-message,
.broadcast-message-preview {
    padding: 10px;
    text-align: center;
    background: #555;
    color: #BBB;
    margin-top: 50px;
}

.card {
    position: relative;
    border: 1px solid #d9d9d9;
    border-radius: 3px;
    color: #666;
    background-color: #fff;
    width: 100%;
    border-radius: 5px;
}

.card .card-header {
    padding: 9px 0;
    height: 40px;
    background: #555;
    color: #fff;
    text-align: center;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.card .card-body {
    padding: 12px 10px;
}

.card .card-footer {
    height: 20px;
    font-size: 10px;
    color: #777;
    margin-top: -15px;
    margin-bottom: 5px;
    margin-left: 20px;
    margin-right: 20px;
}

.card .detail-brand {
    float: left;
    width: 30%;
    line-height: 98px;
    font-size: 30px;
    text-align: center;
    color: white;
}

.card .default {
    background: #1d9d74;
}

.card .info {
    background: #6EBEE7;
}

.card .warn {
    background: #ED7F54;
}

.card .danger {
    background: #6583BE;
}

.card .detail .text-default {
    color: #1d9d74;
}

.card .detail .text-info {
    color: #6EBEE7;
}

.card .detail .text-warn {
    color: #ED7F54;
}

.card .detail .text-danger {
    color: #6583BE;
}

.card .detail {
    float: right;
    width: 70%;
    line-height: 98px;
    text-align: center;
}

.card .detail .text {
    font-size: 12px;
}

.card .detail .number {
    font-size: 30px;
    font-weight: 500;
}

.h100 {
    height: 100px;
}

.inline {
    display: inline;
}

.separator {
    height: 1px;
    background-color: #e5e5e5;
    margin-top: 10px;
}

.card > .card-body > table > thead > tr > td,
.card > .card-body > table > tbody > tr > td {
    word-wrap: break-word;
    word-break: break-all;
}

.card > .card-body > table > thead > tr > td {
    font-weight: 500;
    font-size: 13px;
    text-align: center;
}

.card > .card-body > table > thead > tr > td > span {
    font-weight: 500;
    font-size: 10px;
}

.card > .card-body > table > tbody > tr > td {
    font-size: 12px;
    text-align: center;
}

.card > .card-body > table > tbody > tr > td > a {
    color: #666;
}

.thumbnails > .card > .card-body > table > thead > tr > td,
.thumbnails > .card > .card-body > table > tbody > tr > td {
    font-size: 12px;
    color: #777;
    word-wrap: break-word;
    word-break: break-all;
}

.thumbnails > .card > .card-body > table > thead > tr > td:nth-child(n+2) {
    text-align: center;
}

.thumbnails > .card > .card-body > table > tbody > tr > td:nth-child(n+2) {
    font-weight: 700;
    text-align: center;
}

.thumbnails > .card > .card-body > table > thead > tr > td:nth-child(1),
.thumbnails > .card > .card-body > table > tbody > tr > td:nth-child(1) {
    text-align: left;
}

.tools-header {
    background: whitesmoke;
    padding: 9px 0;
    height: 40px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.tools-header .brand {
    font-size: 13px;
    margin: 2px 10px;
    font-weight: 700;
    float: left;
}

.tools-header .brand > a {
    color: #666;
}

.tools-header > button,
.tools-header > select,
.tools-header > a {
    float: right;
    max-width: 80px;
    margin: 1px 10px;
    height: 25px;
    padding: 0 10px;
    line-height: 25px;
    color: #666;
}

.tools-header .paged {
    margin-right: 0px;
}

.btn {
    height: 32px;
}

.btn.btn-main {
    color: #ffffff;
    background-color: #337ab7;
    border-color: #337ab7;
}

.btn:focus,
.btn:active {
    outline: none !important;
}

.btn-default:hover,
.btn-default:focus,
.btn-default:active {
    color: #1d9d74;
    border-color: #1d9d74;
    background: white;
}


.btn.btn-danger-tag {
    color: #ffffff;
    background-color: #d9534f;
    border-color: #d43f3a;
    line-height: 1px;
    font-size: 11px;
    padding: 4px 4px;
}

.btn.btn-danger {
    color: #333;
    background-color: #fff;
    border-color: #ccc;
}

.btn.btn-danger:hover,
.btn.btn-danger:focus,
.btn.btn-danger:active {
    color: #d9534f;
    border-color: #d9534f;
    background: white;
}

.form-control {
    height : 32px;
}

.form-control:focus {
    border-color: #337ab7;
    box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.075) inset, 0px 0px 0px rgba(29, 157, 116, 1);
}

.form-control {
    border-radius: 8px;
}

.input-label:before {
    display: inline-block;
    content: "*";
    color: #f44336;
    font-family: SimSun;
    font-size: 12px;
    -webkit-transform: TranslateX(-10px);
    -ms-transform: TranslateX(-10px);
    transform: TranslateX(-10px);
}

.label.label-main {
    color: #ffffff;
    background-color: #1d9d74;
    border-color: #1d9d74;
}

.badge-main {
    color: #ffffff;
    background-color: #1d9d74;
    border-color: #1d9d74;
}

.bootstrap-tagsinput {
    background-color: #fff;
    border: 1px solid #ccc;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    display: inline-block;
    padding: 4px 6px;
    color: #555;
    vertical-align: middle;
    border-radius: 4px;
    /* max-width: 100%; */
    width: 85%;
    height: 100px;
    line-height: 20px;
    cursor: text;
}

.bootstrap-tagsinput > .dropdown-menu {
    min-width: 40px;
    font-size: 12px;
}

.bootstrap-tagsinput > .dropdown-menu>.active>a,
.bootstrap-tagsinput > .dropdown-menu>.active>a:focus,
.bootstrap-tagsinput > .dropdown-menu>.active>a:hover {
    background-color: #1d9d74;
    background-image: -webkit-linear-gradient(top, #1d9d74 0, #1d9d74 100%);
    background-image: -o-linear-gradient(top, #1d9d74 0, #1d9d74 100%);
    background-image: -webkit-gradient(linear, left top, left bottom, from(#1d9d74), to(#1d9d74));
    background-image: linear-gradient(to bottom, #1d9d74 0, #1d9d74 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#1d9d74', endColorstr='#1d9d74', GradientType=0);
    background-repeat: repeat-x;
}

.bootstrap-tagsinput > .dropdown-menu>.active>a,
.bootstrap-tagsinput > .dropdown-menu>.active>a:focus,
.bootstrap-tagsinput > .dropdown-menu>.active>a:hover {
    color: #fff;
    text-decoration: none;
    background-color: #1d9d74;
    outline: 0;
}

.bootstrap-tagsinput > .dropdown-menu>.active>a,
.bootstrap-tagsinput > .dropdown-menu>.active>a:hover,
.bootstrap-tagsinput > .dropdown-menu>.active>a:focus {
    color: white;
    text-decoration: none;
    outline: 0;
    background-color: #1d9d74;
}

.inputs-header {
    padding: 9px 0;
    height: 50px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.inputs-header .brand {
    font-size: 13px;
    margin: 2px 10px;
    font-weight: 700;
    float: left;
}

.inputs-header .brand > a {
    color: #666;
}

.inputs-header > input {
    float: right;
    margin: 1px 10px;
    height: 30px;
    padding: 0 10px;
    color: #666;
}

.inputs-header > a {
    float: right;
    margin: 1px 10px;
    height: 30px;
    padding: 5 5px;
}

.inputs-header > select {
    float: right;
    max-width: 80px;
    margin: 1px 10px;
    height: 30px;
    padding: 0 10px;
    color: #666;
    height: 25px;
    font-size: 12px;
}

.witdh-150 {
    max-width: 150px;
}

.witdh-200 {
    max-width: 200px;
}

.width-200 {
    max-width: 200px;
}

.witdh-300 {
    max-width: 300px;
}

.width-300 {
    max-width: 300px;
}

.card.highlight {
    border-color: #d9534f;
}

.card .pagination-footer {
    height: 40px;
    font-size: 10px;
    color: #777;
    margin-top: -15px;
    margin-bottom: 5px;
    margin-left: 20px;
    margin-right: 20px;
}

.card .pagination-footer .tools {
    font-size: 12px;
    margin: 11px 0;
    float: right;
    display: inline;
    margin-right: 20px;
}

.card > .pagination-footer > .tools > span > input {
    height: 25px;
    max-width: 50px;
    display: inline;
}

.pagination {
    display: inline-block;
    padding-left: 0;
    margin: 8px 0;
    float: right;
    border-radius: 4px;
}


.pagination > a {
    margin-right: 5px;
    height: 28px;
    width: 28px;
    padding: 5px 0px;
}

.pagination > .btn.active {
        color: #ffffff;
    background-color: #1d9d74;
    border-color: #1d9d74;
}




.datepicker > .table > thead > tr > td, .datepicker > .table > tbody > tr > td,
.timepicker > .table > thead > tr > td, .timepicker > .table > tbody > tr > td   {
    padding: 5px 3px;
}

.datepicker > .table > thead > tr > td > .btn, .datepicker > .table > tbody > tr > td > .btn,
.timepicker > .table > thead > tr > td > .btn, .timepicker > .table > tbody > tr > td > .btn  {
    border: 1px solid #FFFDFD;
}

.datepicker > .table > thead > tr > td > .btn-default:hover,
.datepicker > .table > thead > tr > td > .btn-default:focus,
.datepicker > .table > thead > tr > td > .btn-default:active,
.datepicker > .table > tbody > tr > td > .btn-default:hover,
.datepicker > .table > tbody > tr > td > .btn-default:focus,
.datepicker > .table > tbody > tr > td > .btn-default:active,
.timepicker > .table > thead > tr > td > .btn-default:hover,
.timepicker > .table > thead > tr > td > .btn-default:focus,
.timepicker > .table > thead > tr > td > .btn-default:active,
.timepicker > .table > tbody > tr > td > .btn-default:hover,
.timepicker > .table > tbody > tr > td > .btn-default:focus,
.timepicker > .table > tbody > tr > td > .btn-default:active  {
    color: #1d9d74;
    border-color: #1d9d74;
    background: white;
}

.datepicker > .table > thead > tr > td > a, .datepicker > .table > tbody > tr > td > a,
.timepicker > .table > thead > tr > td > a, .timepicker > .table > tbody > tr > td > a {
    height: 25px;
    width: 25px;
    padding: 3px 0px;
}

.datepicker > .table > tbody > tr:first-child > td > a {
    padding: 4px 0px;
}

.datepicker > .table > thead > tr > td > a.btn.active, 
.datepicker > .table > tbody > tr > td > a.btn.active,
.timepicker > .table > thead > tr > td > a.btn.active, 
.timepicker > .table > tbody > tr > td > a.btn.active {
/*    color: #ffffff;
    background-color: #1d9d74;
    border-color: #1d9d74;*/
        color: #1d9d74;
    border-color: #1d9d74;
    background: white;
    box-shadow: inset 0 0px 0px rgba(0,0,0,0.125);
}

.datepicker > .table > thead > tr > td:not(:first-child):last-child > a,
.timepicker > .table > thead > tr > td:not(:first-child):last-child > a  {
    height: 25px;
    width: 50px;
    padding: 5px 0px;
}

.datepicker > .table > tbody > tr > td > a, 
.timepicker > .table > tbody > tr > td > a {
    margin-left: 8px;
}


.selectize-input-200 > .selectize-input {
    min-width: 250px;
}

.highlight-border {
    border-color: #337ab7;
    box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.075) inset, 0px 0px 0px rgba(29, 157, 116, 1);
}.browsehappy {
    margin: 0.2em 0;
    background: #ccc;
    color: #000;
    padding: 0.2em 0;
}

body {
    padding: 0;
}


/* Everything but the jumbotron gets side spacing for mobile first views */

.header,
.marketing,
.footer {
    padding-left: 15px;
    padding-right: 15px;
}


/* Custom page header */

.header {
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 10px;
}


/* Make the masthead heading the same height as the navigation */

.header h3 {
    margin-top: 0;
    margin-bottom: 0;
    line-height: 40px;
    padding-bottom: 19px;
}


/* Custom page footer */

.footer {
    padding-top: 19px;
    color: #777;
    border-top: 1px solid #e5e5e5;
}

.container-narrow > hr {
    margin: 30px 0;
}


/* Main marketing message and sign up button */

.jumbotron {
    text-align: center;
    border-bottom: 1px solid #e5e5e5;
}

.jumbotron .btn {
    font-size: 21px;
    padding: 14px 24px;
}


/* Supporting marketing content */

.marketing {
    margin: 40px 0;
}

.marketing p + h4 {
    margin-top: 28px;
}


/* Responsive: Portrait tablets and up */

@media screen and (min-width: 768px) {
    .container {
        width: inherit;
        margin-left: 60px;
        margin-right: 5px;
    }
    /* Remove the padding we set earlier */
    .header,
    .marketing,
    .footer {
        padding-left: 0;
        padding-right: 0;
    }
    /* Space out the masthead */
    .header {
        margin-bottom: 30px;
    }
    /* Remove the bottom border on the jumbotron for visual effect */
    .jumbotron {
        border-bottom: 0;
    }
}

.navbar-inverse {
    background-color: #1d9d74;
    border-color: #1b926c;
}

.navbar-inverse .navbar-nav > li > a {
    color: #b0ddce;
    font-size: 15px;
}

.navbar-inverse .navbar-nav>.open>a,
.navbar-inverse .navbar-nav>.open>a:focus,
.navbar-inverse .navbar-nav>.open>a:hover {
    background-color: #1b926c;
}

@media (min-width: 900px) {
    .navbar-left {
        float: left !important;
    }
    .navbar-right {
        float: right !important;
        margin-right: 0%;
    }
    .navbar-right ~ .navbar-right {
        margin-right: 0;
    }
}

.dropdown-menu {
    min-width: 100px !important;
}

.nav-sidebar li.active a {
    background: #DDD;
}

.dropdown-menu>li>a:hover, .dropdown-menu>li>a:focus {
    background: #1d9d74;
    /*background: #d9d9d9;*/
    color: white;
}

.broadcast-message,
.broadcast-message-preview {
    padding: 10px;
    text-align: center;
    background: #555;
    color: #BBB;
    margin-top: 50px;
}

.card {
    position: relative;
    border: 1px solid #d9d9d9;
    border-radius: 3px;
    color: #666;
    background-color: #fff;
    width: 100%;
    border-radius: 5px;
}

.card .card-header {
    padding: 9px 0;
    height: 40px;
    background: #555;
    color: #fff;
    text-align: center;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.card .card-body {
    padding: 12px 10px;
}

.card .card-footer {
    height: 20px;
    font-size: 10px;
    color: #777;
    margin-top: -15px;
    margin-bottom: 5px;
    margin-left: 20px;
    margin-right: 20px;
}

.card .detail-brand {
    float: left;
    width: 30%;
    line-height: 98px;
    font-size: 30px;
    text-align: center;
    color: white;
}

.card .default {
    background: #1d9d74;
}

.card .info {
    background: #6EBEE7;
}

.card .warn {
    background: #ED7F54;
}

.card .danger {
    background: #6583BE;
}

.card .detail .text-default {
    color: #1d9d74;
}

.card .detail .text-info {
    color: #6EBEE7;
}

.card .detail .text-warn {
    color: #ED7F54;
}

.card .detail .text-danger {
    color: #6583BE;
}

.card .detail {
    float: right;
    width: 70%;
    line-height: 98px;
    text-align: center;
}

.card .detail .text {
    font-size: 12px;
}

.card .detail .number {
    font-size: 30px;
    font-weight: 500;
}

.h100 {
    height: 100px;
}

.inline {
    display: inline;
}

.separator {
    height: 1px;
    background-color: #e5e5e5;
    margin-top: 10px;
}

.card > .card-body > table > thead > tr > td,
.card > .card-body > table > tbody > tr > td {
    word-wrap: break-word;
    word-break: break-all;
}

.card > .card-body > table > thead > tr > td {
    font-weight: 500;
    font-size: 13px;
    text-align: center;
}

.card > .card-body > table > thead > tr > td > span {
    font-weight: 500;
    font-size: 10px;
}

.card > .card-body > table > tbody > tr > td {
    font-size: 12px;
    text-align: center;
}

.card > .card-body > table > tbody > tr > td > a {
    color: #666;
}

.thumbnails > .card > .card-body > table > thead > tr > td,
.thumbnails > .card > .card-body > table > tbody > tr > td {
    font-size: 12px;
    color: #777;
    word-wrap: break-word;
    word-break: break-all;
}

.thumbnails > .card > .card-body > table > thead > tr > td:nth-child(n+2) {
    text-align: center;
}

.thumbnails > .card > .card-body > table > tbody > tr > td:nth-child(n+2) {
    font-weight: 700;
    text-align: center;
}

.thumbnails > .card > .card-body > table > thead > tr > td:nth-child(1),
.thumbnails > .card > .card-body > table > tbody > tr > td:nth-child(1) {
    text-align: left;
}

.tools-header {
    background: whitesmoke;
    padding: 9px 0;
    height: 40px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.tools-header .brand {
    font-size: 13px;
    margin: 2px 10px;
    font-weight: 700;
    float: left;
}

.tools-header .brand > a {
    color: #666;
}

.tools-header > button,
.tools-header > select,
.tools-header > a {
    float: right;
    max-width: 80px;
    margin: 1px 10px;
    height: 25px;
    padding: 0 10px;
    line-height: 25px;
    color: #666;
}

.tools-header .paged {
    margin-right: 0px;
}

.btn.btn-main {
    color: #ffffff;
    background-color: #1d9d74;
    border-color: #1d9d74;
}

.btn:focus,
.btn:active {
    outline: none !important;
}

.btn-default:hover,
.btn-default:focus,
.btn-default:active {
    color: #1d9d74;
    border-color: #1d9d74;
    background: white;
}

.btn-default-inverse {
    color: #1d9d74;
    border-color: #1d9d74;
    background: white;
}

.btn-default-inverse:hover,
.btn-default-inverse:focus,
.btn-default:active {
    color: #1d9d74;
    border-color: #1d9d74;
    background: white;
}

.btn.btn-danger-tag {
    color: #ffffff;
    background-color: #d9534f;
    border-color: #d43f3a;
    line-height: 1px;
    font-size: 11px;
    padding: 4px 4px;
}

.btn.btn-danger {
    color: #333;
    background-color: #fff;
    border-color: #ccc;
}

.btn.btn-danger:hover,
.btn.btn-danger:focus,
.btn.btn-danger:active {
    color: #d9534f;
    border-color: #d9534f;
    background: white;
}

.form-control {
    height : 32px;
}

.form-control:focus {
    border-color: #1d9d74;
    box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.075) inset, 0px 0px 0px rgba(29, 157, 116, 1);
}

.form-control {
    border-radius: 8px;
}

.input-label:before {
    display: inline-block;
    content: "*";
    color: #f44336;
    font-family: SimSun;
    font-size: 12px;
    -webkit-transform: TranslateX(-10px);
    -ms-transform: TranslateX(-10px);
    transform: TranslateX(-10px);
}

.label.label-main {
    color: #ffffff;
    background-color: #1d9d74;
    border-color: #1d9d74;
}

.badge-main {
    color: #ffffff;
    background-color: #1d9d74;
    border-color: #1d9d74;
}

.bootstrap-tagsinput {
    background-color: #fff;
    border: 1px solid #ccc;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    display: inline-block;
    padding: 4px 6px;
    color: #555;
    vertical-align: middle;
    border-radius: 4px;
    /* max-width: 100%; */
    width: 85%;
    height: 100px;
    line-height: 20px;
    cursor: text;
}

.bootstrap-tagsinput > .dropdown-menu {
    min-width: 40px;
    font-size: 12px;
}

.bootstrap-tagsinput > .dropdown-menu>.active>a,
.bootstrap-tagsinput > .dropdown-menu>.active>a:focus,
.bootstrap-tagsinput > .dropdown-menu>.active>a:hover {
    background-color: #1d9d74;
    background-image: -webkit-linear-gradient(top, #1d9d74 0, #1d9d74 100%);
    background-image: -o-linear-gradient(top, #1d9d74 0, #1d9d74 100%);
    background-image: -webkit-gradient(linear, left top, left bottom, from(#1d9d74), to(#1d9d74));
    background-image: linear-gradient(to bottom, #1d9d74 0, #1d9d74 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#1d9d74', endColorstr='#1d9d74', GradientType=0);
    background-repeat: repeat-x;
}

.bootstrap-tagsinput > .dropdown-menu>.active>a,
.bootstrap-tagsinput > .dropdown-menu>.active>a:focus,
.bootstrap-tagsinput > .dropdown-menu>.active>a:hover {
    color: #fff;
    text-decoration: none;
    background-color: #1d9d74;
    outline: 0;
}

.bootstrap-tagsinput > .dropdown-menu>.active>a,
.bootstrap-tagsinput > .dropdown-menu>.active>a:hover,
.bootstrap-tagsinput > .dropdown-menu>.active>a:focus {
    color: white;
    text-decoration: none;
    outline: 0;
    background-color: #1d9d74;
}

.inputs-header {
    padding: 9px 0;
    height: 50px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.inputs-header .brand {
    font-size: 13px;
    margin: 2px 10px;
    font-weight: 700;
    float: left;
}

.inputs-header .brand > a {
    color: #666;
}

.inputs-header > input {
    float: right;
    margin: 1px 10px;
    height: 30px;
    padding: 0 10px;
    color: #666;
}

.inputs-header > a {
    float: right;
    margin: 1px 10px;
    height: 30px;
    padding: 5 5px;
}

.inputs-header > select {
    float: right;
    max-width: 80px;
    margin: 1px 10px;
    height: 30px;
    padding: 0 10px;
    color: #666;
    height: 25px;
    font-size: 12px;
}

.witdh-150 {
    max-width: 150px;
}

.witdh-200 {
    max-width: 200px;
}

.card.highlight {
    border-color: #d9534f;
}

.card .pagination-footer {
    height: 40px;
    font-size: 10px;
    color: #777;
    margin-top: -15px;
    margin-bottom: 5px;
    margin-left: 20px;
    margin-right: 20px;
}

.card .pagination-footer .tools {
    font-size: 12px;
    margin: 11px 0;
    float: right;
    display: inline;
    margin-right: 20px;
}

.card > .pagination-footer > .tools > span > input {
    height: 25px;
    max-width: 50px;
    display: inline;
}

.pagination {
    display: inline-block;
    padding-left: 0;
    margin: 8px 0;
    float: right;
    border-radius: 4px;
}


.pagination > a {
    margin-right: 5px;
    height: 28px;
    width: 28px;
    padding: 5px 0px;
}

.pagination > .btn.active {
        color: #ffffff;
    background-color: #449d44;
    border-color: #449d44;
}




.datepicker > .table > thead > tr > td, .datepicker > .table > tbody > tr > td,
.timepicker > .table > thead > tr > td, .timepicker > .table > tbody > tr > td   {
    padding: 5px 3px;
}

.datepicker > .table > thead > tr > td > .btn, .datepicker > .table > tbody > tr > td > .btn,
.timepicker > .table > thead > tr > td > .btn, .timepicker > .table > tbody > tr > td > .btn  {
    border: 1px solid #FFFDFD;
}

.datepicker > .table > thead > tr > td > .btn-default:hover,
.datepicker > .table > thead > tr > td > .btn-default:focus,
.datepicker > .table > thead > tr > td > .btn-default:active,
.datepicker > .table > tbody > tr > td > .btn-default:hover,
.datepicker > .table > tbody > tr > td > .btn-default:focus,
.datepicker > .table > tbody > tr > td > .btn-default:active,
.timepicker > .table > thead > tr > td > .btn-default:hover,
.timepicker > .table > thead > tr > td > .btn-default:focus,
.timepicker > .table > thead > tr > td > .btn-default:active,
.timepicker > .table > tbody > tr > td > .btn-default:hover,
.timepicker > .table > tbody > tr > td > .btn-default:focus,
.timepicker > .table > tbody > tr > td > .btn-default:active  {
    color: #1d9d74;
    border-color: #1d9d74;
    background: white;
}

.datepicker > .table > thead > tr > td > a, .datepicker > .table > tbody > tr > td > a,
.timepicker > .table > thead > tr > td > a, .timepicker > .table > tbody > tr > td > a {
    height: 25px;
    width: 25px;
    padding: 3px 0px;
}

.datepicker > .table > tbody > tr:first-child > td > a {
    padding: 4px 0px;
}

.datepicker > .table > thead > tr > td > a.btn.active, 
.datepicker > .table > tbody > tr > td > a.btn.active,
.timepicker > .table > thead > tr > td > a.btn.active, 
.timepicker > .table > tbody > tr > td > a.btn.active {
/*    color: #ffffff;
    background-color: #1d9d74;
    border-color: #1d9d74;*/
        color: #1d9d74;
    border-color: #1d9d74;
    background: white;
    box-shadow: inset 0 0px 0px rgba(0,0,0,0.125);
}

.datepicker > .table > thead > tr > td:not(:first-child):last-child > a,
.timepicker > .table > thead > tr > td:not(:first-child):last-child > a  {
    height: 25px;
    width: 50px;
    padding: 5px 0px;
}

.datepicker > .table > tbody > tr > td > a, 
.timepicker > .table > tbody > tr > td > a {
    margin-left: 8px;
}


.selectize-input-200 > .selectize-input {
    min-width: 250px;
}

.highlight-border {
    border-color: #1d9d74;
    box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.075) inset, 0px 0px 0px rgba(29, 157, 116, 1);
}


.sortorder:after {
  content: '\25b2';   
}
.sortorder.reverse:after {
  content: '\25bc';   
}



.input-control select {
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  position: relative;
  border: 1px #d9d9d9 solid;
  width: 100%;
  height: 100%;
  padding: .3125rem;
  z-index: 0;
}

.navbar-inverse {
    background-color: #337ab7;
    border-color: #337ab7;
}

.sidebar {
    z-index: 1;
    width: 220px;
    /*position: fixed;*/
    top: 0;
    left: 0;
    height: 100%;
}

#page-wrapper {
    position: inherit;
    margin: 70px 0 0 220px;
    padding: 12px 30px;
    border-left: 0px solid #e7e7e7;
}

.sidebar .sidebar-nav.navbar-collapse {
    padding-right: 0;
    padding-left: 0;
    background-color: #F5F5F5;
    position: relative;
    color: black;
    width: 100%;
    padding: 0;
    margin: 0;
    list-style: none inside none;
}

.sidebar a {
    color: #555;
}

.sidebar ul li:hover {
    color:red;
}

.form-control {
    border-radius: 8px;
}

.form-control:focus {
    border-color: #337ab7;
    box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.075) inset, 0px 0px 0px rgba(29, 157, 116, 1);
}

.highlight-border {
    border-color: #337ab7;
    box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.075) inset, 0px 0px 0px rgba(29, 157, 116, 1);
}.browsehappy {
    margin: 0.2em 0;
    background: #ccc;
    color: #000;
    padding: 0.2em 0;
}

.btn.btn-main {
    color: #ffffff;
    background-color: #337ab7;
    border-color: #337ab7;
}

.btn-default-inverse {
    color: #337ab7;
    border-color: #337ab7;
    background: white;
}

.btn-default-inverse:hover,
.btn-default-inverse:focus,
.btn-default:active {
    color: #337ab7;
    border-color: #337ab7;
    background: white;
}

.btn-danger-inverse {
    color: #d9534f;
    border-color: #d9534f;
    background: white;
}

.btn-danger-inverse:hover,
.btn-danger-inverse:focus,
.btn-danger:active {
    color: #d9534f;
    border-color: #d9534f;
    background: white;
}

.btn-tab-active,
.btn-tab-active:hover,
.btn-tab-active:focus, 
.btn-tab-default:hover,
.btn-tab-default:focus,
.btn-tab-default:active {
    color: #337ab7;
    border-color: #337ab7;
    background: white;
    font-weight: 600;
}
.btn-tab-default {
    color: #777;
    background: white;
    font-weight: 600;
}

.pagination > .btn.active {
    color: #ffffff;
    background-color: #337ab7;
    border-color: #337ab7;
}

.btn-default:hover, .btn-default:focus, .btn-default:active {
    color: #337ab7;
    border-color: #337ab7;
    background: white;
}

.bootstrap-switch.bootstrap-switch-on {
    border-color: #337ab7;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success, .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success {
    color: #fff;
    background: #337ab7;
}

.selectize-input-200 > .selectize-input {
    min-width: 200px;
    border-color: #337ab7;
}

.btn-outline-primary {
    color: #007bff;
    background-color: transparent;
    background-image: none;
    border-color: #007bff;
}

.btn-outline-primary:hover {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-outline-primary:focus, .btn-outline-primary.focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}

.btn-outline-primary.disabled, .btn-outline-primary:disabled {
    color: #007bff;
    background-color: transparent;
}

.btn-outline-primary:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active,
.show > .btn-outline-primary.dropdown-toggle {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-outline-primary:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-primary.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}

.btn-outline-secondary {
    color: #6c757d;
    background-color: transparent;
    background-image: none;
    border-color: #6c757d;
}

.btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-outline-secondary:focus, .btn-outline-secondary.focus {
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

.btn-outline-secondary.disabled, .btn-outline-secondary:disabled {
    color: #6c757d;
    background-color: transparent;
}

.btn-outline-secondary:not(:disabled):not(.disabled):active, .btn-outline-secondary:not(:disabled):not(.disabled).active,
.show > .btn-outline-secondary.dropdown-toggle {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-secondary.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

.btn-outline-success {
    color: #28a745;
    background-color: transparent;
    background-image: none;
    border-color: #28a745;
}

.btn-outline-success:hover {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;
}

.btn-outline-success:focus, .btn-outline-success.focus {
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

.btn-outline-success.disabled, .btn-outline-success:disabled {
    color: #28a745;
    background-color: transparent;
}

.btn-outline-success:not(:disabled):not(.disabled):active, .btn-outline-success:not(:disabled):not(.disabled).active,
.show > .btn-outline-success.dropdown-toggle {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;
}

.btn-outline-success:not(:disabled):not(.disabled):active:focus, .btn-outline-success:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-success.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

.btn-outline-info {
    color: #17a2b8;
    background-color: transparent;
    background-image: none;
    border-color: #17a2b8;
}

.btn-outline-info:hover {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.btn-outline-info:focus, .btn-outline-info.focus {
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.btn-outline-info.disabled, .btn-outline-info:disabled {
    color: #17a2b8;
    background-color: transparent;
}

.btn-outline-info:not(:disabled):not(.disabled):active, .btn-outline-info:not(:disabled):not(.disabled).active,
.show > .btn-outline-info.dropdown-toggle {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.btn-outline-info:not(:disabled):not(.disabled):active:focus, .btn-outline-info:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-info.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.btn-outline-warning {
    color: #ffc107;
    background-color: transparent;
    background-image: none;
    border-color: #ffc107;
}

.btn-outline-warning:hover {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107;
}

.btn-outline-warning:focus, .btn-outline-warning.focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

.btn-outline-warning.disabled, .btn-outline-warning:disabled {
    color: #ffc107;
    background-color: transparent;
}

.btn-outline-warning:not(:disabled):not(.disabled):active, .btn-outline-warning:not(:disabled):not(.disabled).active,
.show > .btn-outline-warning.dropdown-toggle {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107;
}

.btn-outline-warning:not(:disabled):not(.disabled):active:focus, .btn-outline-warning:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-warning.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

.btn-outline-danger {
    color: #dc3545;
    background-color: transparent;
    background-image: none;
    border-color: #dc3545;
}

.btn-outline-danger:hover {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-outline-danger:focus, .btn-outline-danger.focus {
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}

.btn-outline-danger.disabled, .btn-outline-danger:disabled {
    color: #dc3545;
    background-color: transparent;
}

.btn-outline-danger:not(:disabled):not(.disabled):active, .btn-outline-danger:not(:disabled):not(.disabled).active,
.show > .btn-outline-danger.dropdown-toggle {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-outline-danger:not(:disabled):not(.disabled):active:focus, .btn-outline-danger:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-danger.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}

.btn-outline-light {
    color: #f8f9fa;
    background-color: transparent;
    background-image: none;
    border-color: #f8f9fa;
}

.btn-outline-light:hover {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #f8f9fa;
}

.btn-outline-light:focus, .btn-outline-light.focus {
    box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

.btn-outline-light.disabled, .btn-outline-light:disabled {
    color: #f8f9fa;
    background-color: transparent;
}

.btn-outline-light:not(:disabled):not(.disabled):active, .btn-outline-light:not(:disabled):not(.disabled).active,
.show > .btn-outline-light.dropdown-toggle {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #f8f9fa;
}

.btn-outline-light:not(:disabled):not(.disabled):active:focus, .btn-outline-light:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-light.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

.btn-outline-dark {
    color: #343a40;
    background-color: transparent;
    background-image: none;
    border-color: #343a40;
}

.btn-outline-dark:hover {
    color: #fff;
    background-color: #343a40;
    border-color: #343a40;
}

.btn-outline-dark:focus, .btn-outline-dark.focus {
    box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

.btn-outline-dark.disabled, .btn-outline-dark:disabled {
    color: #343a40;
    background-color: transparent;
}

.btn-outline-dark:not(:disabled):not(.disabled):active, .btn-outline-dark:not(:disabled):not(.disabled).active,
.show > .btn-outline-dark.dropdown-toggle {
    color: #fff;
    background-color: #343a40;
    border-color: #343a40;
}

.btn-outline-dark:not(:disabled):not(.disabled):active:focus, .btn-outline-dark:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-dark.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}
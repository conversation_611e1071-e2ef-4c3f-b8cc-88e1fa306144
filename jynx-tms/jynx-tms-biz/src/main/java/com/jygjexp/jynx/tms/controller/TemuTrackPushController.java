package com.jygjexp.jynx.tms.controller;

import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.PushNodeMsgDto;
import com.jygjexp.jynx.tms.service.TmsOrderTrackNewService;
import com.jygjexp.jynx.tms.service.impl.TmsOrderTrackNewServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Temu客户订单轨迹推送控制器
 * 
 * <AUTHOR>
 * @date 2025/09/05
 */
@Slf4j
@RestController
@RequestMapping("/temu/track")
@RequiredArgsConstructor
@Tag(name = "Temu轨迹推送", description = "Temu客户订单轨迹推送相关接口")
public class TemuTrackPushController {
    
    private final TmsOrderTrackNewService tmsOrderTrackNewService;
    private final TmsOrderTrackNewServiceImpl tmsOrderTrackNewServiceImpl;
    
    /**
     * 手动推送Temu轨迹数据
     * 
     * @param pushNodeMsgList 轨迹推送消息列表
     * @return 推送结果
     */
    @PostMapping("/push")
    @Operation(summary = "推送Temu轨迹数据", description = "手动推送Temu客户订单轨迹数据到Temu API")
    public R pushTemuTrackData(@RequestBody List<PushNodeMsgDto> pushNodeMsgList) {
        log.info("接收到Temu轨迹推送请求，数据量: {}", pushNodeMsgList != null ? pushNodeMsgList.size() : 0);
        return R.ok(tmsOrderTrackNewService.pushTemuTrackData(pushNodeMsgList));
    }
    
    /**
     * 查询待推送的Temu轨迹数据
     * 
     * @return 待推送的轨迹数据列表
     */
    @GetMapping("/pending")
    @Operation(summary = "查询待推送数据", description = "查询待推送的Temu客户订单轨迹数据")
    public R queryPendingTemuTrackData() {
        log.info("接收到查询待推送Temu轨迹数据请求");
        return tmsOrderTrackNewServiceImpl.queryPendingTemuTrackData();
    }
    
    /**
     * 手动执行Temu轨迹推送任务
     * 
     * @return 推送结果
     */
    @PostMapping("/execute")
    @Operation(summary = "执行推送任务", description = "手动执行Temu轨迹推送任务（查询+推送）")
    public R executeTemuTrackPushTask() {
        log.info("接收到手动执行Temu轨迹推送任务请求");
        return tmsOrderTrackNewServiceImpl.executeTemuTrackPushTask();
    }
}

package com.jygjexp.jynx.tms.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Temu API客户端工具类
 * 
 * <AUTHOR>
 * @date 2025/09/05
 */
@Slf4j
@Component
public class TemuApiClient {
    
    // Temu API配置
    private static final String TEMU_API_URL = "https://openapi-b-global.temu.com/ark/router";
    private static final String CLIENT_ID = "061ad71a8e1df39f234a59ced5fae8c0";
    private static final String TARGET_CLIENT_ID = "218ddd7a02e04ef875ea4d006fc4e1b4";
    private static final String CLIENT_SECRET = "356452fd24e2769e62bd15fccf2ce04e0c77c7ee";
    private static final String TYPE = "bg.logistics.package.track.info.push";
    
    // 代理配置
    private static final String PROXY_HOST = "**********";
    private static final int PROXY_PORT = 8000;
    private static final String PROXY_USERNAME = "JiaYou";
    private static final String PROXY_PASSWORD = "JiaYou";
    
    private OkHttpClient client;
    
    /**
     * 获取配置了代理的HTTP客户端
     */
    private OkHttpClient getHttpClient() {
        if (client == null) {
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            
            // 配置HTTP代理
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(PROXY_HOST, PROXY_PORT));
            builder.proxy(proxy);
            
            // 配置代理认证
            Authenticator proxyAuthenticator = (route, response) -> {
                String credential = Credentials.basic(PROXY_USERNAME, PROXY_PASSWORD);
                return response.request().newBuilder()
                        .header("Proxy-Authorization", credential)
                        .build();
            };
            builder.proxyAuthenticator(proxyAuthenticator);
            
            client = builder.build();
        }
        return client;
    }
    
    /**
     * 推送轨迹数据到Temu API
     * 
     * @param packageSn 包裹号
     * @param trackingNumber 跟踪号
     * @param trackData 轨迹数据
     * @return 推送结果
     */
    public TemuApiResponse pushTrackData(String packageSn, String trackingNumber, Object trackData) {
        try {
            // 构建请求参数
            Map<String, Object> requestData = buildRequestData(packageSn, trackingNumber, trackData);
            
            // 构建HTTP请求
            String jsonBody = JSONUtil.toJsonStr(requestData);
            RequestBody body = RequestBody.create(jsonBody, MediaType.get("application/json; charset=utf-8"));
            
            Request request = new Request.Builder()
                    .url(TEMU_API_URL)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            
            // 发送请求
            try (Response response = getHttpClient().newCall(request).execute()) {
                String responseBody = response.body() != null ? response.body().string() : "";
                log.info("Temu API推送响应 - 状态码: {}, 响应体: {}", response.code(), responseBody);
                if (response.isSuccessful()) {
                    return TemuApiResponse.success(responseBody);
                } else {
                    return TemuApiResponse.failure("HTTP错误: " + response.code(), responseBody);
                }
            }
            
        } catch (IOException e) {
            log.error("Temu API推送异常 - 包裹号: {}, 跟踪号: {}", packageSn, trackingNumber, e);
            return TemuApiResponse.failure("网络异常: " + e.getMessage(), null);
        } catch (Exception e) {
            log.error("Temu API推送未知异常 - 包裹号: {}, 跟踪号: {}", packageSn, trackingNumber, e);
            return TemuApiResponse.failure("未知异常: " + e.getMessage(), null);
        }
    }
    
    /**
     * 构建Temu API请求数据
     */
    private Map<String, Object> buildRequestData(String packageSn, String trackingNumber, Object trackData) {
        long timestamp = Instant.now().getEpochSecond();
        String uuid = UUID.randomUUID().toString();

        // 构建签名字符串
        String signString = CLIENT_ID + CLIENT_SECRET + timestamp + uuid;
        String sign = MD5.create().digestHex(signString).toUpperCase();

        Map<String, Object> data = new HashMap<>();
        data.put("target_client_id", TARGET_CLIENT_ID);
        data.put("packageSn", StrUtil.blankToDefault(packageSn, ""));
        data.put("sign", sign);
        data.put("type", TYPE);
        data.put("trackingNumber", StrUtil.blankToDefault(trackingNumber, ""));
        data.put("uuid", uuid);
        data.put("client_id", CLIENT_ID);
        data.put("timestamp", timestamp);
        data.put("data", trackData);
        return data;
    }

    
    /**
     * Temu API响应结果封装类
     */
    public static class TemuApiResponse {
        private boolean success;
        private String message;
        private String responseBody;
        
        private TemuApiResponse(boolean success, String message, String responseBody) {
            this.success = success;
            this.message = message;
            this.responseBody = responseBody;
        }
        
        public static TemuApiResponse success(String responseBody) {
            return new TemuApiResponse(true, "推送成功", responseBody);
        }
        
        public static TemuApiResponse failure(String message, String responseBody) {
            return new TemuApiResponse(false, message, responseBody);
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public String getMessage() {
            return message;
        }
        
        public String getResponseBody() {
            return responseBody;
        }
    }
}

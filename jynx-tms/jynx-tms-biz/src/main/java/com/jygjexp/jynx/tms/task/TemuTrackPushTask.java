package com.jygjexp.jynx.tms.task;

import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.service.impl.TmsOrderTrackNewServiceImpl;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Temu客户订单轨迹推送定时任务
 * 
 * <AUTHOR>
 * @date 2025/09/05
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TemuTrackPushTask {
    
    private final TmsOrderTrackNewServiceImpl tmsOrderTrackNewService;
    
    /**
     * Temu轨迹推送定时任务
     * 任务配置：每5分钟执行一次
     **/
    @SneakyThrows
    @XxlJob("temuTrackPushTask")
    public void executeTemuTrackPush() {
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("=== Temu轨迹推送定时任务开始执行 ===");
            XxlJobHelper.log("=== Temu轨迹推送定时任务开始执行 ===");
            
            // 执行推送任务
            R result = tmsOrderTrackNewService.executeTemuTrackPushTask();
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            if (result.isOk()) {
                String successMsg = String.format("Temu轨迹推送任务执行成功，耗时: %d ms，结果: %s", 
                        duration, result.getData());
                log.info(successMsg);
                XxlJobHelper.log(successMsg);
                XxlJobHelper.handleSuccess(successMsg);
            } else {
                String errorMsg = String.format("Temu轨迹推送任务执行失败，耗时: %d ms，错误: %s", 
                        duration, result.getMsg());
                log.error(errorMsg);
                XxlJobHelper.log(errorMsg);
                XxlJobHelper.handleFail(errorMsg);
            }
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            String errorMsg = String.format("Temu轨迹推送任务执行异常，耗时: %d ms", duration);
            log.error(errorMsg, e);
            XxlJobHelper.log(errorMsg + "，异常信息: " + e.getMessage());
            XxlJobHelper.handleFail(errorMsg);
        } finally {
            log.info("=== Temu轨迹推送定时任务执行结束 ===");
        }
    }
}

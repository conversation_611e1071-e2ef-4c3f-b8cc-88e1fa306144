package com.jygjexp.jynx.tms.service;

import cn.hutool.json.JSONArray;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.PushNodeMsgDto;

import java.util.List;
import java.util.Set;

/**
 * @Author: xiongpengfei
 * @Description: 中大件轨迹接口装载方法接口
 * @Date: 2025/8/19 10:03
 */
public interface TmsOrderTrackNewService {

    // ==================== 迁移自zxoms的轨迹查询接口 ====================

    /**
     * 批量订单轨迹查询 - 迁移自zxoms
     * @param pkgNos 包裹号列表，逗号分隔
     * @param zip 邮编（可选）
     * @return 批量轨迹查询结果
     */
    R getTracksFromZxoms(String pkgNos, String zip);


    // 原zxoms官网查询接口兼容查询nb中大件（N开头的单号）
    void processNOrderNos(List<String> nOrderNos, String zipInput, JSONArray ja, Set<String> handledPkgNos);

    /**
     * 批量订单轨迹查询 - 新接口（支持限流）
     * @param orderNos 订单号列表
     * @return 批量轨迹查询结果
     */
    R batchTrackOrders(List<String> orderNos);

    /**
     * Temu客户订单轨迹推送接口
     * @param pushNodeMsgList 轨迹推送消息列表
     * @return 推送结果
     */
    R pushTemuTrackData(List<PushNodeMsgDto> pushNodeMsgList);
}

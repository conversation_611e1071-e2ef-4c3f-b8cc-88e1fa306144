package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsTrackWebhookEntity;
import com.jygjexp.jynx.tms.event.TrackSavedEvent;
import com.jygjexp.jynx.tms.feign.RemoteTmsUpmsService;
import com.jygjexp.jynx.tms.mapper.TmsTrackWebhookMapper;
import com.jygjexp.jynx.tms.mongo.entity.TmsOrderTrackText;
import com.jygjexp.jynx.tms.mongo.service.TmsTrackTextService;
import com.jygjexp.jynx.tms.service.TmsCustomerOrderService;
import com.jygjexp.jynx.tms.service.TmsTrackWebhookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;

/**
 * 轨迹webhook
 *
 * <AUTHOR>
 * @date 2025-08-18 13:56:15
 */
@Service
public class TmsTrackWebhookServiceImpl extends ServiceImpl<TmsTrackWebhookMapper, TmsTrackWebhookEntity> implements TmsTrackWebhookService {

    @Autowired
    private  TmsTrackTextService tmsTrackTextService;
    @Autowired
    private TmsCustomerOrderService tmsCustomerOrderService;

    //根据单号插入或更新轨迹
    @Async
    @Override
    public void saveOrUpdate(String orderNo, String trackText) {
        //查询该单号是否已经存在记录，存在则更新
        LambdaQueryWrapper<TmsTrackWebhookEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsTrackWebhookEntity::getOrderNo, orderNo)
        .last("LIMIT 1");
        TmsTrackWebhookEntity entity = this.getOne(wrapper);
        if (entity == null ) {
            String trackId = UUID.randomUUID().toString();
            //插入MongoDB
            TmsOrderTrackText tmsOrderTrackText = new TmsOrderTrackText();
            tmsOrderTrackText.setId(trackId);
            tmsOrderTrackText.setOrderNo(orderNo);
            tmsOrderTrackText.setTracking(trackText);
            tmsOrderTrackText.setTrackingTime(new Date());
            tmsTrackTextService.save(tmsOrderTrackText);
            //插入Mysql
            entity = new TmsTrackWebhookEntity();
            entity.setOrderNo(orderNo);
            entity.setCustomerId(tmsCustomerOrderService.getCustomerOrderBySubOrder(orderNo).getCustomerId());
            entity.setTrackId(trackId);
            entity.setStatus(0);
            entity.setIsPush(0);
            entity.setPushStatus("未推送");
            this.save(entity);
        }else {
            TmsOrderTrackText tmsOrderTrackText = new TmsOrderTrackText();
            tmsOrderTrackText.setId(entity.getTrackId());
            tmsOrderTrackText.setOrderNo(orderNo);
            tmsOrderTrackText.setTracking(trackText);
            tmsOrderTrackText.setTrackingTime(new Date());
            tmsTrackTextService.updateById(tmsOrderTrackText);
            LambdaUpdateWrapper<TmsTrackWebhookEntity> webhookUpdateWrapper = new LambdaUpdateWrapper<>();
            webhookUpdateWrapper.eq(TmsTrackWebhookEntity::getOrderNo, orderNo)
            .set(TmsTrackWebhookEntity::getUpdateTime, LocalDateTime.now())
            .set(TmsTrackWebhookEntity::getIsPush, 0);

            this.update(webhookUpdateWrapper);
        }
    }

    /**
     * 监听轨迹保存事件
     */
    @Async
    @EventListener
    public void onTrackSaved(TrackSavedEvent event) {
        int retry = 3;
        R track = null;
        for (int i = 0; i < retry; i++) {
            track = tmsCustomerOrderService.track(event.getOrderNo());
            if (track.isOk() && track.getCode() != 1 && track.getData() != null) {
                event.setTrackText(JSONUtil.toJsonStr(track.getData()));
                break;
            }
            try {
                Thread.sleep(1000); // 等待后重试
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return;
            }
        }
        saveOrUpdate(event.getOrderNo(), event.getTrackText());
    }

}
# TMS系统 - Temu客户订单轨迹推送功能

## 功能概述

为TMS系统中的Temu客户订单实现轨迹推送功能，支持自动查询待推送数据并调用Temu API进行轨迹推送。

## 实现架构

### 核心组件

1. **TmsOrderTrackNewService** - 轨迹推送服务接口
2. **TmsOrderTrackNewServiceImpl** - 轨迹推送服务实现
3. **TrackSavedEventEntity** - MongoDB轨迹事件实体
4. **TrackSavedEventService** - MongoDB轨迹事件服务
5. **TemuApiClient** - Temu API客户端工具类
6. **TemuTrackPushTask** - 定时任务
7. **TemuTrackPushController** - REST API控制器

### 数据流程

```
MongoDB(TrackSavedEvent) -> 查询isPush=0 -> 筛选Temu客户 -> 构建推送数据 -> 调用Temu API -> 更新推送状态
```

## 配置信息

### Temu API配置
- **URL**: `https://openapi-b-global.temu.com/ark/router`
- **client_id**: `061ad71a8e1df39f234a59ced5fae8c0`
- **target_client_id**: `218ddd7a02e04ef875ea4d006fc4e1b4`
- **client_secret**: `356452fd24e2769e62bd15fccf2ce04e0c77c7ee`
- **type**: `bg.logistics.package.track.info.push`

### 网络代理配置
- **代理类型**: HTTP代理
- **IP**: `**********`
- **端口**: `8000`
- **用户名**: `JiaYou`
- **密码**: `JiaYou`

### Temu客户识别
- **customerId = 60** 或 **customerId = 66** 的订单被识别为Temu客户订单

## API接口

### 1. 手动推送轨迹数据
```http
POST /temu/track/push
Content-Type: application/json

[
  {
    "cid": 60,
    "orderId": 12345,
    "wno": "运单号",
    "cno": "客户单号",
    "pathCode": 520,
    "pathDate": "2025-09-04T00:00:00",
    "pathInfo": "Delivered",
    "pathLocation": "STE-JEANNE-D'ARC-DE-MATANE",
    "country": "CA",
    "timeZone": "GMT-04:00"
  }
]
```

### 2. 查询待推送数据
```http
GET /temu/track/pending
```

### 3. 执行推送任务
```http
POST /temu/track/execute
```

## 定时任务配置

### XXL-Job配置
- **任务名称**: `temuTrackPushTask`
- **建议Cron表达式**: `0 */5 * * * ?` (每5分钟执行一次)
- **任务描述**: Temu客户订单轨迹推送定时任务

## 数据库结构

### MongoDB集合: TrackSavedEvent
```javascript
{
  "_id": "ObjectId",
  "orderNo": "订单号",
  "trackText": "轨迹JSON数据",
  "isPush": 0,  // 0=未推送，1=已推送
  "pushStatus": "推送状态",
  "createTime": "创建时间",
  "updateTime": "更新时间"
}
```

### MySQL表: tms_customer_order
- 通过 `customerId` 字段识别Temu客户
- `customerId = 60` 或 `customerId = 66` 为Temu客户

## Temu API请求格式

```json
{
  "target_client_id": "218ddd7a02e04ef875ea4d006fc4e1b4",
  "packageSn": "BG-25082450VHVE0FZX",
  "sign": "C147EDABDED5D3961AA10E99BF855B17",
  "type": "bg.logistics.package.track.info.push",
  "trackingNumber": "1029777131209696",
  "uuid": "b11612c8-1976-465e-ad9a-07657bc27eca",
  "client_id": "061ad71a8e1df39f234a59ced5fae8c0",
  "timestamp": 1757054304,
  "data": [
    {
      "description": "Delivered",
      "eventCity": "",
      "eventCode": "520",
      "eventCountry": "CA",
      "eventLocation": "STE-JEANNE-D'ARC-DE-MATANE",
      "eventProvince": "",
      "flightNo": "",
      "operationTime": "2025-09-04 00:00:00",
      "tailDescription": "Delivered",
      "timeZone": "GMT-04:00",
      "transferFlag": 1,
      "transferNo": "1029777131209696",
      "transferService": "CANADAPOST"
    }
  ]
}
```

## 使用说明

### 1. 自动推送（推荐）
配置定时任务，系统会自动：
1. 查询MongoDB中 `isPush=0` 的轨迹事件
2. 根据订单号查询订单信息
3. 筛选Temu客户订单（customerId = 60 或 66）
4. 构建推送数据并调用Temu API
5. 更新推送状态

### 2. 手动推送
通过REST API手动触发推送：
- 查询待推送数据：`GET /temu/track/pending`
- 执行推送任务：`POST /temu/track/execute`
- 直接推送数据：`POST /temu/track/push`

### 3. 测试验证
运行测试类 `TemuTrackPushTest` 验证功能：
```bash
mvn test -Dtest=TemuTrackPushTest
```

## 注意事项

1. **网络代理必须配置**：Temu API仅支持国外服务器IP访问
2. **客户ID筛选**：只有customerId为60或66的订单才会推送
3. **推送状态管理**：成功推送后会更新isPush状态为1
4. **错误处理**：推送失败会记录错误信息，不影响其他订单推送
5. **签名算法**：使用MD5加密 `client_id + client_secret + timestamp + uuid`

## 监控和日志

- 所有推送操作都有详细日志记录
- 定时任务执行结果会记录到XXL-Job平台
- 推送成功/失败统计信息会在返回结果中体现
- MongoDB中的推送状态可用于数据统计和监控

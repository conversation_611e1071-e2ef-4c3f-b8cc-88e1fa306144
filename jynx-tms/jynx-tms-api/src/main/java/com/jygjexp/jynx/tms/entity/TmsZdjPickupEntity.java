package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 中大件自提信息记录表
 *
 * <AUTHOR>
 * @date 2025-06-06 17:03:21
 */
@Data
@TenantTable
@TableName("tms_zdj_pickup")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "中大件自提信息记录表")
public class TmsZdjPickupEntity extends BaseLogicEntity<TmsZdjPickupEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 订单号
	*/
    @Schema(description="订单号")
    private String orderNo;

	/**
	* 取件码
	*/
    @Schema(description="取件码")
    private String pickupCode;

	/**
	* 仓库id
	*/
    @Schema(description="仓库id")
    private Long warehouseId;

	/**
	* 仓位
	*/
    @Schema(description="仓位")
    private String warehouseLocation;
	/**
	 * 仓位id
	 */
	@Schema(description="仓位id")
	private Long locationId;

	/**
	* 收件人
	*/
    @Schema(description="收件人")
    private String recipientName;

	/**
	* 收件电话
	*/
    @Schema(description="收件电话")
    private String recipientPhone;

	/**
	* 预约取货时间开始
	*/
    @Schema(description="预约取货时间开始")
    private LocalDateTime appointmentTime;

	/**
	 * 预约取货时间结束
	 */
	@Schema(description="预约取货时间结束")
	private LocalDateTime appointmentTimeEnd;

	/**
	* 上架天数
	*/
    @Schema(description="上架天数")
    private Integer shelvingDays;

	/**
	* 上架时间
	*/
    @Schema(description="上架时间")
    private LocalDateTime shelvingTime;
	/**
	 * 自提时间
	 */
	@Schema(description="自提时间")
	private LocalDateTime pickupTime;

	/**
	 * 订单总件数
	 */
	@Schema(description="订单总件数")
	private Integer totalCount;
	/**
	 * 已扫描数
	 */
	@Schema(description="已扫描数")
	private Integer scanCount;

	/**
	* 是否可销毁（0：否 1：是）
	*/
    @Schema(description="是否可销毁（0：否 1：是）")
    private Integer isExpiredDestroy;

	/**
	* 自提状态（0 待联系、1 预约待取货、2 已取件、3 重新派送、4 销毁状态）
	*/
    @Schema(description="自提状态（0 待联系、1 预约待取货、2 已取件、3 重新派送、4 销毁状态）")
    private Integer pickupStatus;

	/**
	* 自提图片
	*/
    @Schema(description="自提图片")
    private String pickupImages;
	/**
	 *
	 */

}
package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 附加费表达式表
 *
 * <AUTHOR>
 * @date 2025-07-14 15:12:38
 */
@Data
@TableName("tms_fee_rule_expr")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "附加费表达式表")
public class TmsFeeRuleExprEntity extends Model<TmsFeeRuleExprEntity> {

	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 费用ID，关联fee_rule表的主键
	*/
    @Schema(description="费用ID，关联fee_rule表的主键")
    private Long feeId;

	/** 左值，例如：30 */
	@Schema(description = "左值")
	private BigDecimal leftValue;

	/** 左操作符，例如：>=、>、<、<= */
	@Schema(description = "左符号 例如：>=、>、<、<=")
	private String leftOperator;

	/** 变量名，例如：weight、distance、volume 等 */
	@Schema(description = "计算条件字段，例如：weight、length、volume 等")
	private String variableName;

	/** 右操作符，例如：<、<=、== 等 */
	@Schema(description = "右符号 例如：>=、>、<、<=")
	private String rightOperator;

	/** 右值，例如：60 */
	@Schema(description = "右值")
	private BigDecimal rightValue;

	/** 与下一条规则之间的连接符（AND / OR），最后一条为空 */
	@Schema(description = "连接符（&& / ||）")
	private String connector;

	//** 拼接后的表达式片段（如：30 <= weight && weight < 60）
	@Schema(description = "拼接表达式值")
	private String expression;
}
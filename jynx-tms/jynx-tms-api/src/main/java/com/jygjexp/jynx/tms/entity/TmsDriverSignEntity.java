package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 司机签到表
 *
 * <AUTHOR>
 * @date 2025-07-15 11:21:20
 */
@Data
@TenantTable
@TableName("tms_driver_sign")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "司机签到表")
public class TmsDriverSignEntity extends Model<TmsDriverSignEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	@Schema(description = "司机id")
	private Long driverId;

	/**
	* 司机号
	*/
    @Schema(description="司机号")
    private String driverNum;

	/**
	* 姓名
	*/
    @Schema(description="姓名")
    private String driverName;

	/**
	* 省份
	*/
    @Schema(description="省份")
    private String province;

	/**
	* 出勤日期
	*/
    @Schema(description="出勤日期")
    private LocalDate attendanceDate;

	/**
	* 上班时间
	*/
    @Schema(description="上班时间")
    private LocalDateTime workTime;

	/**
	* 经度(上班)
	*/
    @Schema(description="经度(上班)")
    private String workLatitude;

	/**
	* 纬度(上班)
	*/
    @Schema(description="纬度(上班)")
    private String workLongitude;

	@TableField(exist = false)
	private String workAddress;

	/**
	* 下班时间
	*/
    @Schema(description="下班时间")
    private LocalDateTime offWorkTime;

	/**
	* 经度(下班)
	*/
    @Schema(description="经度(下班)")
    private String offWorkLatitude;

	/**
	* 纬度(下班)
	*/
    @Schema(description="纬度(下班)")
    private String offWorkLongitude;

	@TableField(exist = false)
	private String offWorkAddress;


	/**
	* 出勤工时
	*/
    @Schema(description="出勤工时")
    private BigDecimal attendanceHours;

	/**
	 * 最后订单完成时间
	 */
	@Schema(description="最后订单完成时间")
	private LocalDateTime finalOrderCompletionTime;

	/**
	 * 状态
	 */
	@Schema(description="状态")
	private Integer status;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Long revision;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标志
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Long tenantId;
}
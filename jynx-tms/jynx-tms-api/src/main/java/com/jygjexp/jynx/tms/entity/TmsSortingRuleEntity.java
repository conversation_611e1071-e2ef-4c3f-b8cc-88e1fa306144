package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 分拣规则表
 *
 * <AUTHOR>
 * @date 2025-06-12 20:04:40
 */
@Data
@TenantTable
@TableName("tms_sorting_rule")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "分拣规则表")
public class TmsSortingRuleEntity extends Model<TmsSortingRuleEntity> {


	/**
	* id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private Long id;

	/**
	* 模版id
	*/
    @Schema(description="模版id")
    private Long templateId;

	/**
	 * 格口id
	 */
	@Schema(description="格口id")
	private Long gridId;

	/**
	 * 业务类型（1:揽收退件、2：正向派件）
	 */
	@Schema(description="业务类型（1:揽收退件、2：正向派件）")
	private Integer businessType;

	/**
	 * 分拣规则类型（1：按商家、2：按路线号、3：按条件）
	 */
	@Schema(description="分拣规则类型（1：按商家、2：按路线号、3：按条件）")
	private Integer sortingRuleType;

	/**
	* 商家编码-authId（字符串id，多个逗号分隔，如：1,3,5）
	*/
    @Schema(description="商家编码-authId（字符串id，多个逗号分隔，如：1,3,5）")
    private String merchantCode;

	/**
	* 商家名称
	*/
    @Schema(description="商家名称")
    private String merchantName;

	/**
	* 路线编号
	*/
    @Schema(description="路线编号")
    private String routeNumber;

	/**
	* 仓库名称
	*/
    @Schema(description="仓库名称")
    private String warehouseName;

	/**
	* 分拣按条件时-条件SPEL表达式（分拣时判断使用）
	*/
    @Schema(description="分拣按条件时-条件SPEL表达式（分拣时判断使用）")
    private String conditionSpel;

	/**
	* 分拣按条件时-Json串（返回给前端展示使用）
	*/
    @Schema(description="分拣按条件时-Json串（返回给前端展示使用）")
    private String conditions;

	/**
	* 状态
	*/
    @Schema(description="状态")
    private Integer status;

	/**
	* 站点id
	*/
    @Schema(description="站点id")
    private Integer siteId;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Integer revision;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 逻辑删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="逻辑删除")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Integer tenantId;
}
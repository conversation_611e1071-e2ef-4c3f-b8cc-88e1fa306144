package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 轨迹节点维护
 *
 * <AUTHOR>
 * @date 2025-05-16 18:03:16
 */
@Data
@TenantTable
@TableName("tms_track_node")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "轨迹节点维护")
public class TmsTrackNodeEntity extends BaseLogicEntity<TmsTrackNodeEntity> {


	/**
	* 节点主键id
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="节点主键id")
    private Long nodeId;

	/**
	* 节点代码
	*/
    @Schema(description="节点代码")
    private Integer nodeCode;

	/**
	* 节点名称
	*/
    @Schema(description="节点名称")
    private String nodeName;

	/**
	* 内部节点内容
	*/
    @Schema(description="内部节点内容")
    private String nodeContent;


	/**
	 * 外部节点内容
	 */
	@Schema(description="外部节点内容")
	private String nodeOutContent;

	/**
	* 触发环节
	*/
    @Schema(description="触发环节")
    private Integer nodeLink;

	/**
	* 轨迹类型 0:内外部 1：内部
	*/
    @Schema(description="轨迹类型 0:内外部 1：内部")
    private Integer trackType;

	/**
	* 节点类型 0 手动/ 1自动节点
	*/
    @Schema(description="节点类型 0 手动/ 1自动节点")
    private Integer nodeType;

	/**
	* 节点启用状态：0：禁用、1：启用
	*/
    @Schema(description="节点启用状态：0：禁用、1：启用")
    private Integer isValid;

}
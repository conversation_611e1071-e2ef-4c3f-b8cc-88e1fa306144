package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * web端预先路线规划
 *
 * <AUTHOR>
 * @date 2025-04-07 14:46:30
 */
@Data
@TenantTable
@TableName("tms_pre_route_plan")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "web端预先路线规划")
public class TmsPreRoutePlanEntity extends Model<TmsPreRoutePlanEntity> {


	/**
	* 预先路线规划id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="预先路线规划id")
    private Long id;

	/**
	* 路线名称（运输批次）
	*/
    @Schema(description="路线名称（运输批次）")
    private String routeName;

	/**
	* 路线类型（派送、揽收、干线，暂时只有揽收）
	*/
    @Schema(description="路线类型（派送、揽收、干线，暂时只有派送）")
    private Integer routeType;

	/**
	* 路线规划中包含订单数量
	*/
    @Schema(description="路线规划中包含订单数量")
    private Integer orderCount;

	/**
	* 路线规划中包含司机数
	*/
    @Schema(description="路线规划中包含司机数")
    private Integer driverCount;
	/**
	 * 区域id
	 */
	@Schema(description="区域id")
	private Long areaId;
	/**
	 * 仓库id
	 */
	@Schema(description="仓库id")
	private Long warehouseId;

	/**
	* 状态
	*/
    @Schema(description="状态")
    private Integer status;


	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Integer revision;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 逻辑删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="逻辑删除")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Integer tenantId;
}
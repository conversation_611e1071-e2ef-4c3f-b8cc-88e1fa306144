package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jygjexp.jynx.common.core.util.TenantTable;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 卡派车辆信息
 *
 * <AUTHOR>
 * @date 2025-02-24 18:41:19
 */
@Data
@TenantTable
@TableName("tms_vehicle_info")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卡派车辆信息")
public class TmsVehicleInfoEntity extends BaseLogicEntity<TmsVehicleInfoEntity> {


    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 车主
     */
    @Schema(description = "车主ID")
    private Long driverId;

    /**
     * 承运商ID
     */
    @Schema(description = "承运商ID")
    private Long carrierId;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String contactPhone;

    /**
     * 车牌号
     */
    @Schema(description = "车牌号")
    private String licensePlate;

    /**
     * 车辆类型
     */
    @Schema(description = "车辆类型")
    private Integer vehicleType;

    /**
     * 车辆颜色
     */
    @Schema(description = "车辆颜色")
    private String vehicleColor;

    /**
     * 车辆图片(存URL路径)
     */
    @Schema(description = "车辆图片(存URL路径)")
    private String vehicleImageUrl;

    /**
     * 载重(吨)
     */
    @Schema(description = "载重(吨)")
    private BigDecimal loadCapacity;

    /**
     * 容积(升)
     */
    @Schema(description = "容积(升)")
    private BigDecimal volume;

    /**
     * 车辆购买日期
     */
    @Schema(description = "车辆购买日期")
    private LocalDate purchaseDate;

    /**
     * 车辆购买年份
     */
    @Schema(description = "车辆购买年份")
    private String purchaseYear;

    /**
     * 车辆登记日期
     */
    @Schema(description = "车辆登记日期")
    private LocalDate registrationDate;

    /**
     * 车辆保险有限期(开始时间)
     */
    @Schema(description = "车辆保险有限期(开始时间)")
    private LocalDate insuranceStartDate;

    /**
     * 车辆保险有限期(结束时间)
     */
    @Schema(description = "车辆保险有限期(结束时间)")
    private LocalDate insuranceEndDate;

    /**
     * 车辆保险单文件(支持图片和文件(word、PDF最大支持60MB))
     */
    @Schema(description = "车辆保险单文件(支持图片和文件(word、PDF最大支持60MB))")
    private String insuranceDocumentUrl;

    /**
     * 长(m)
     */
    @Schema(description = "长(m)")
    private BigDecimal length;

    /**
     * 宽(m)
     */
    @Schema(description = "宽(m)")
    private BigDecimal width;

    /**
     * 高(m)
     */
    @Schema(description = "高(m)")
    private BigDecimal height;

    /**
     * 可配货物类型
     */
    @Schema(description = "可配货物类型")
    private String cargoType;

    /**
     * 启用状态：0 禁用，1 启用
     */
    @Schema(description = "启用状态：0 禁用，1 启用")
    private Integer isValid;

    /**
     * 车辆所属权：1个人，2公司
     */
    @Schema(description = "车辆所属权：1个人，2公司")
    private Integer ownershipType;

    /**
     * 货仓长(m)
     */
    @Schema(description = "货仓长(m)")
    private BigDecimal warehouseLength;

    /**
     * 货仓宽(m)
     */
    @Schema(description = "货仓宽(m)")
    private BigDecimal warehouseWidth;

    /**
     * 货仓高(m)
     */
    @Schema(description = "货仓高(m)")
    private BigDecimal warehouseHeight;

}
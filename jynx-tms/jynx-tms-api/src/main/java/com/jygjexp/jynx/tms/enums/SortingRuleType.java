package com.jygjexp.jynx.tms.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum SortingRuleType {
    /**
     * 按商家
     */
    MERCHANT(1, "MERCHANT"),
    /**
     * 按路线号
     */
    ROUTE_NUMBER(2, "ROUTE_NUMBER"),
    /**
     * 按条件
     */
    CONDITION(3, "CONDITION");

    private final Integer code;
    private final String value;

    SortingRuleType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    // 枚举缓存 map
    private static final Map<Integer, SortingRuleType> LOOKUP = new HashMap<>();

    static {
        for (SortingRuleType type : SortingRuleType.values()) {
            LOOKUP.put(type.getCode(), type);
        }
    }

    /**
     * 根据 code 获取枚举项
     */
    public static SortingRuleType lookup(Integer code) {
        return LOOKUP.get(code);
    }
}

package com.jygjexp.jynx.tms.utils;

import java.math.BigDecimal;

/**
 * Haversine 公式计算两经纬度点之间的球面距离（地球距离）
 * 支持 double 和 BigDecimal 输入，返回米和千米
 *
 * 用法示例：
 *   double km = HaversineUtil.distanceKm(lat1, lng1, lat2, lng2);
 *   double m = HaversineUtil.distanceMeter(lat1, lng1, lat2, lng2);
 */
public class HaversineUtil {
    // 地球半径（单位：米）
    private static final double EARTH_RADIUS_M = 6371000.0;
    // 地球半径（单位：千米）
    private static final double EARTH_RADIUS_KM = 6371.0;

    /**
     * 计算两点间球面距离（单位：米）
     */
    public static double distanceMeter(double lat1, double lng1, double lat2, double lng2) {
        return haversine(lat1, lng1, lat2, lng2, EARTH_RADIUS_M);
    }

    /**
     * 计算两点间球面距离（单位：千米）
     */
    public static double distanceKm(double lat1, double lng1, double lat2, double lng2) {
        return haversine(lat1, lng1, lat2, lng2, EARTH_RADIUS_KM);
    }

    /**
     * 支持 BigDecimal 输入，返回米
     */
    public static double distanceMeter(BigDecimal lat1, BigDecimal lng1, BigDecimal lat2, BigDecimal lng2) {
        return distanceMeter(lat1.doubleValue(), lng1.doubleValue(), lat2.doubleValue(), lng2.doubleValue());
    }

    /**
     * 支持 BigDecimal 输入，返回千米
     */
    public static double distanceKm(BigDecimal lat1, BigDecimal lng1, BigDecimal lat2, BigDecimal lng2) {
        return distanceKm(lat1.doubleValue(), lng1.doubleValue(), lat2.doubleValue(), lng2.doubleValue());
    }

    /**
     * Haversine 公式核心实现
     * @param lat1 起点纬度
     * @param lng1 起点经度
     * @param lat2 终点纬度
     * @param lng2 终点经度
     * @param radius 地球半径（米或千米）
     * @return 距离
     */
    private static double haversine(double lat1, double lng1, double lat2, double lng2, double radius) {
        double lat1Rad = Math.toRadians(lat1);
        double lat2Rad = Math.toRadians(lat2);
        double deltaLat = lat2Rad - lat1Rad;
        double deltaLng = Math.toRadians(lng2 - lng1);

        double a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2)
                + Math.cos(lat1Rad) * Math.cos(lat2Rad)
                * Math.sin(deltaLng / 2) * Math.sin(deltaLng / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return radius * c;
    }

    /**
     * 经纬度点结构体（可选，便于扩展）
     */
    public static class LatLng {
        public final double lat;
        public final double lng;
        public LatLng(double lat, double lng) {
            this.lat = lat;
            this.lng = lng;
        }
    }

    /**
     * 计算两点间球面距离（单位：米），支持 LatLng
     */
    public static double distanceMeter(LatLng p1, LatLng p2) {
        return distanceMeter(p1.lat, p1.lng, p2.lat, p2.lng);
    }

    /**
     * 计算两点间球面距离（单位：千米），支持 LatLng
     */
    public static double distanceKm(LatLng p1, LatLng p2) {
        return distanceKm(p1.lat, p1.lng, p2.lat, p2.lng);
    }
}

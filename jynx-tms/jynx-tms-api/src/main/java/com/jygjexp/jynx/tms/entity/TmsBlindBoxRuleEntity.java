package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 盲盒服务商规则配置表
 *
 * <AUTHOR>
 * @date 2025-07-18 18:00:59
 */
@Data
@TableName("tms_blind_box_rule")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "盲盒服务商规则配置表")
public class TmsBlindBoxRuleEntity extends Model<TmsBlindBoxRuleEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 盲盒ID，关联tms_blind_box.id
	*/
    @Schema(description="盲盒ID，关联tms_blind_box.id")
    private Long blindBoxId;

	/**
	* 服务商ID
	*/
    @Schema(description="服务商ID")
    private Long providerId;

	/**
	* 服务商名称
	*/
    @Schema(description="服务商名称")
    private String providerName;

	/**
	* 利润率（例如0.12表示12%）
	*/
    @Schema(description="利润率（例如12表示12%）")
    private BigDecimal profitRate;

	/**
	* 排序值（可用于优先级排序）
	*/
    @Schema(description="排序值（可用于优先级排序）")
    private Integer sort;

	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description = "创建人")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description = "创建时间")
	private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.UPDATE)
	@Schema(description = "更新人")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.UPDATE)
	@Schema(description = "更新时间")
	private LocalDateTime updateTime;
}
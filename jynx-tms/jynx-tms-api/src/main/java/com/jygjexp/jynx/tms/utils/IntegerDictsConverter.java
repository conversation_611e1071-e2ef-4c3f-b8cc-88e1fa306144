package com.jygjexp.jynx.tms.utils;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.jygjexp.jynx.admin.api.entity.SysDictItem;
import com.jygjexp.jynx.common.core.constant.SecurityConstants;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.core.util.SpringContextHolder;
import com.jygjexp.jynx.tms.annotation.ConvertType;
import com.jygjexp.jynx.tms.constants.DictConvertConstants;
import com.jygjexp.jynx.tms.entity.SysDictItemEntity;
import com.jygjexp.jynx.tms.feign.RemoteUpmsService;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.List;


/**
 * 字典转换器
 */
public class IntegerDictsConverter implements Converter<Integer> {

    private StringRedisTemplate redisTemplate;
    private RemoteUpmsService remoteUpmsService;


    public IntegerDictsConverter() {
        // 从 Spring 容器中获取 RedisTemplate
        this.redisTemplate = SpringContextHolder.getBean(StringRedisTemplate.class);
        this.remoteUpmsService = SpringContextHolder.getBean(RemoteUpmsService.class);
    }

    @Override
    public Class<Integer> supportJavaTypeKey() {
        return Integer.class;
    }


    //获取字典值(公用方法，经过多次优化，如果没有bug请勿修改)
    private String getDictValue(Integer value, String type) {
        LocalCacheMap cache = LocalCacheMap.getInstance();
        String data = (String) cache.get(DictConvertConstants.BASE_KEY + type + value);
        if (data == null) {
            R<List<SysDictItem>> dictByType = remoteUpmsService.getDictByType(type, SecurityConstants.FROM_IN);
            List<SysDictItem> dict = dictByType.getData();
            for (SysDictItem sysDictItem : dict) {
                //把全部数据放入LocalCacheMap中
                cache.set(DictConvertConstants.BASE_KEY + type + sysDictItem.getItemValue(), sysDictItem.getLabel());
                if (sysDictItem.getItemValue().equals(value.toString())) {
                    return sysDictItem.getLabel();
                }
            }
        }
        return data == null ? "" : data;
    }


    //路由转换方法
    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        ConvertType annotation = contentProperty.getField().getAnnotation(ConvertType.class);
        if (annotation == null) {
            return new WriteCellData<>(value.toString());
        }
        String type = annotation.value();
        switch (type) {
            case "warehouseId":
                return new WriteCellData<>(convertWarehouseId(value));
            case "returnFlag":
                return new WriteCellData<>(convertPostName(value));
            case "auth":
                return new WriteCellData<>(convertSubChannel(value));
            case "status":
                return new WriteCellData<>(convertStatus(value));
            case "boolean":
                return new WriteCellData<>(convertBoolean(value));
            case "employeeType":
                return new WriteCellData<>(convertEmployeeType(value));
            case "postModel":
                return new WriteCellData<>(convertPostModel(value));
            case "transferType":
                return new WriteCellData<>(convertTransferType(value));
            case "PrintStatus":
                return new WriteCellData<>(convertPrintStatus(value));
            case "orderStatusZdj":
                return new WriteCellData<>(convertOrderStatusZdj(value));
            case "receiveType":
                return new WriteCellData<>(convertReceiveType(value));
            case "orderType":
                return new WriteCellData<>(convertOrderType(value));
            case "transportType":
                return new WriteCellData<>(convertTransportType(value));
            case "addressType":
                return new WriteCellData<>(convertAddressType(value));
            case "cargoType":
                return new WriteCellData<>(convertCargoType(value));
            case "businessModel":
                return new WriteCellData<>(convertBusinessModel(value));
            case "customerPush":
                return new WriteCellData<>(convertCustomerPush(value));
            case "taskStatus":
                return new WriteCellData<>(convertTaskStatus(value));
            case "driverType":
                return new WriteCellData<>(convertDriverType(value));
            case "driverWorkType":
                return new WriteCellData<>(convertDriverWorkType(value));
            case "licenseType":
                return new WriteCellData<>(convertLicenseType(value));
            case "billingModel":
                return new WriteCellData<>(convertBillingModel(value));
            case "tmsIsValid":
                return new WriteCellData<>(convertTmsIsValid(value));
            case "lmddriverAudit":
                return new WriteCellData<>(convertDriverAuditStatus(value));
            case "levelMembership":
                return new WriteCellData<>(convertLevelMembership(value));
            case "feeType":
                return new WriteCellData<>(convertFeeType(value));
            case "storeType":
                return new WriteCellData<>(convertStoreType(value));
            case "settlementType":
                return new WriteCellData<>(convertSettlementType(value));
            case "payType":
                return new WriteCellData<>(convertPayType(value));
            case "customerLevel":
                return new WriteCellData<>(convertCustomerLevel(value));
            case "customerType":
                return new WriteCellData<>(convertCustomerType(value));
            case "boxRule":
                return new WriteCellData<>(convertBoxRule(value));
            case "storeOrderStatus":
                return new WriteCellData<>(convertStoreOrderStatus(value));
            case "scriptStatus":
                return new WriteCellData<>(convertScriptStatus(value));
            case "writeOffFlag":
                return new WriteCellData<>(convertWriteOffFlag(value));
            case "withdrawalStatus":
                return new WriteCellData<>(convertWithdrawalStatus(value));
            case "mainFlag":
                return new WriteCellData<>(convertMainFlag(value));
            case "adjustmentType":
                return new WriteCellData<>(convertAdjustmentType(value));
            default:
                return new WriteCellData<>(value.toString());
        }
    }

    //核销状态
    private String convertWriteOffFlag(Integer value) {
        if(0 == value){
            return "未核销";
        }else if(1 == value){
            return "已核销";
        }
        return null;
    }
    // 是否为总部主单 - 分拣
    private String convertMainFlag(Integer value) {
        if(0 == value){
            return "否";
        }else if(1 == value){
            return "是";
        }
        return null;
    }
    // 打印凭证状态
    private String convertScriptStatus(Integer value) {
        if(0 == value){
            return "未打印";
        }else if(1 == value){
            return "已打印";
        }else{
            return "未知状态";
        }
    }

    // 快递业务订单状态
    private String convertStoreOrderStatus(Integer value) {
        return getDictValue(value, DictConvertConstants.STORE_ORDER_STATUS);
    }

    // 盲盒比价规则
    private String convertBoxRule(Integer value) {
        return getDictValue(value, DictConvertConstants.TMS_BOX_RULE);
    }

    // 附加费费用类型
    private String convertFeeType(Integer value) {
        return getDictValue(value, DictConvertConstants.FEE_TYPE);
    }

    // 中大件服务商报价会员等级
    private String convertLevelMembership(Integer value) {
        return getDictValue(value, DictConvertConstants.LEVEL_MEMBERSHIP);
    }

    // 中大件审核状态
    private String convertDriverAuditStatus(Integer value) {
        return getDictValue(value, DictConvertConstants.DRIVER_AUDIT_STATUS);
    }

    // 中大件启用停用状态
    private String convertTmsIsValid(Integer value) {
        return getDictValue(value, DictConvertConstants.TMS_IS_VALID);
    }

    // 中大件司机计费模式
    private String convertBillingModel(Integer value) {
        return getDictValue(value, DictConvertConstants.BILLING_MODEL_ZDJ);
    }

    // 中大件司机驾照类型
    private String convertLicenseType(Integer value) {
        return getDictValue(value, DictConvertConstants.LICENSE_TYPE_ZDJ);
    }

    // 中大件司机工作方式
    private String convertDriverWorkType(Integer value) {
        return getDictValue(value, DictConvertConstants.DRIVER_WORK_TYPE_ZDJ);
    }

    // 中大件司机类型
    private String convertDriverType(Integer value) {
        return getDictValue(value, DictConvertConstants.DRIVER_TYPE_ZDJ);
    }

    //运输任务状态
    private String convertTaskStatus(Integer value) {
        return getDictValue(value, DictConvertConstants.TASK_STATUS);
    }


    //渠道类型
    private String convertCustomerPush(Integer value) {
        return getDictValue(value, DictConvertConstants.CHANNEL_TYPE_ZDJ);
    }


    //业务模式
    private String convertBusinessModel(Integer value) {
        return getDictValue(value, DictConvertConstants.BUSINESS_MODEL_ZDJ);
    }


    //地址类型
    private String convertAddressType(Integer value) {
        return getDictValue(value, DictConvertConstants.ADDRESS_TYPE_ZDJ);
    }

    //货物类型
    private String convertCargoType(Integer value) {
        return getDictValue(value, DictConvertConstants.CARGO_TYPE_ZDJ);
    }


    //运输类型
    private String convertTransportType(Integer value) {
        return getDictValue(value, DictConvertConstants.TRANSPORT_TYPE_ZDJ);
    }


    //托盘或者包裹类型
    private String convertOrderType(Integer value) {
        return getDictValue(value, DictConvertConstants.ORDER_TYPE_ZDJ);
    }

    //收货类型
    private String convertReceiveType(Integer value) {
        return getDictValue(value, DictConvertConstants.RECEIVE_TYPE_ZDJ);
    }

    //中大件订单状态
    private String convertOrderStatusZdj(Integer value) {
        return getDictValue(value, DictConvertConstants.ORDER_STATUS_ZDJ);

    }

    //转换仓库id
    private String convertWarehouseId(Integer warehouseId) {
        return getDictValue(warehouseId, DictConvertConstants.WAREHOUSE);
    }


    //转换打印机故障类型
    private String convertPrintStatus(Integer status) {
        return getDictValue(status, DictConvertConstants.PRINT_STATE);
    }

    //转换返仓类型
    private String convertPostName(Integer flag) {
        return getDictValue(flag, DictConvertConstants.RETURN_ORDER_TYPE);
    }

    //转换子渠道
    private String convertSubChannel(Integer auth) {
        return getDictValue(auth, DictConvertConstants.RETURN_AUTH);
    }

    //转换订单状态
    private String convertStatus(Integer status) {
        return getDictValue(status, DictConvertConstants.RETURN_STATUS);
    }


    //转换员工类型
    private String convertEmployeeType(Integer type) {
        return getDictValue(type, DictConvertConstants.STATION_EMPLOYEE_TYPE);
    }


    //转换布尔值
    private String convertBoolean(Integer status) {
        return status == 0 ? "否" : "是";
    }


    //转换驿站类型
    private String convertPostModel(Integer type) {
        return getDictValue(type, DictConvertConstants.STATION_MANAGE_TYPE);
    }


    //转换转账类型
    private String convertTransferType(Integer type) {
        return getDictValue(type, DictConvertConstants.STATION_TRANSFER_TYPE);
    }

    //转换门店类型
    private String convertStoreType(Integer type) {
        return getDictValue(type, DictConvertConstants.TMS_STORE_TYPE);
    }

    //转换结算方式
    private String convertSettlementType(Integer type) {
        return getDictValue(type, DictConvertConstants.TMS_STORE_SETTLEMENT_TYPE);
    }

    //转换支付方式
    private String convertPayType(Integer type) {
        return getDictValue(type, DictConvertConstants.TMS_STORE_PAY_TYPE);
    }

    //转换客户等级
    private String convertCustomerLevel(Integer type) {
        return getDictValue(type, DictConvertConstants.TMS_STORE_CUSTOMER_RANK);
    }

    //转换客户类型
    private String convertCustomerType(Integer type) {
        return getDictValue(type, DictConvertConstants.TMS_STORE_CUSTOMER_TYPE);
    }

    // 推广人结算状态
    private String convertWithdrawalStatus(Integer type) {
        return getDictValue(type, DictConvertConstants.TMS_STORE_PROMOTION_SETTLE_STATUS);
    }

    //转换调账类型
    private String convertAdjustmentType(Integer value) {
        return value == 0 ? "上调" : "下调";
    }

    public List<SysDictItemEntity> getDictValue(String data) {
        return JsonParser.getDictValue(data);
    }


}

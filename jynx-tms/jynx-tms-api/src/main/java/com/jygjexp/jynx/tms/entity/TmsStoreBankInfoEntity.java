package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 门店收款银行信息表
 *
 * <AUTHOR>
 * @date 2025-07-09 11:16:38
 */
@Data
@TenantTable
@TableName("tms_store_bank_info")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门店收款银行信息表")
public class TmsStoreBankInfoEntity extends Model<TmsStoreBankInfoEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 门店代码（外键）
	*/
    @Schema(description="门店ID")
    private Long storeId;

	/**
	* 收款信息类型（商家、个人）
	*/
    @Schema(description="收款信息类型（商家、个人）")
    private String accountType;

	/**
	* Institution Number
	*/
    @Schema(description="Institution Number")
    private String institutionNumber;

	/**
	* Transit Number
	*/
    @Schema(description="Transit Number")
    private String transitNumber;

	/**
	* Account Number
	*/
    @Schema(description="Account Number")
    private String accountNumber;

	/**
	* Account Name
	*/
    @Schema(description="Account Name")
    private String accountName;

	/**
	* 是否默认账户：0、是；1、不是
	*/
	@Schema(description="是否默认账户：0、是；1、不是")
	private Integer isDefaultAccount;
	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Long revision;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标志
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Long tenantId;
}
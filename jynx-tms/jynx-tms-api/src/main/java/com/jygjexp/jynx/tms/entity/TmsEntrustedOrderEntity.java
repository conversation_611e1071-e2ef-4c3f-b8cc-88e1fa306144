package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import com.jygjexp.jynx.tms.enums.AdditionalServiceType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 卡派委托订单
 *
 * <AUTHOR>
 * @date 2025-03-04 18:43:07
 */
@Data
@TenantTable
@TableName("tms_entrusted_order")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卡派委托订单")
public class TmsEntrustedOrderEntity extends BaseLogicEntity<TmsEntrustedOrderEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 委托单号
	*/
    @Schema(description="委托单号")
    private String entrustedOrderNumber;

	/**
	 * 客户单号
	 */
	@Schema(description="客户单号")
	private String customerOrderNumber;

	/**
	 * 是否子单号
	 */
	@Schema(description = "是否子单号")
	private Boolean isSubOrderNo;

	/**
	 * 运输单号
	 */
	@Schema(description="运输单号")
	private String shipmentNo;

	/**
	 * 客户ID
	 */
	@Schema(description = "客户ID")
	private Long customerId;

	/**
	* 订单状态(23001=待指派、23002=已取消、23003=已指派、23004=待审批、23005=待分配、23006=已驳回、23007=已指派、23008=待运输、23009=待收货、23010=运输中、23011=已完成)
	*/
    @Schema(description="订单状态(23001=待指派、23002=已取消、23003=已指派、23004=待审批、23005=待分配、23006=已驳回、23007=已指派、23008=待运输、23009=待收货、23010=运输中、23011=已完成)")
    private Integer orderStatus;

	/**
	 * 审核状态
	 */
	@Schema(description = "审核状态")
	private Integer auditStatus;

	/**
	* 发货人姓名
	*/
    @Schema(description="发货人姓名")
    private String shipperName;

	/**
	* 发货人电话
	*/
    @Schema(description="发货人电话")
    private String shipperPhone;

	/**
	* 始发地
	*/
    @Schema(description="始发地")
    private String origin;

	/**
	* 发货邮编
	*/
    @Schema(description="发货邮编")
    private String shipperPostalCode;

	/**
	* 发货详细地址
	*/
    @Schema(description="发货详细地址")
    private String shipperAddress;

	/**
	* 预计发货时间开始
	*/
    @Schema(description="预计发货时间开始")
    private LocalDateTime estimatedShippingTimeStart;

	/**
	 * 预计发货时间结束
	 */
	@Schema(description="预计发货时间结束")
	private LocalDateTime estimatedShippingTimeEnd;

	/**
	* 到货人姓名
	*/
    @Schema(description="到货人姓名")
    private String receiverName;

	/**
	* 到货人电话
	*/
    @Schema(description="到货人电话")
    private String receiverPhone;

	/**
	* 目的地
	*/
    @Schema(description="目的地")
    private String destination;

	/**
	* 到货邮编
	*/
    @Schema(description="到货邮编")
    private String destPostalCode;

	/**
	* 到货详细地址
	*/
    @Schema(description="到货详细地址")
    private String destAddress;

	/**
	* 预计到货时间开始
	*/
    @Schema(description="预计到货时间开始")
    private LocalDateTime estimatedArrivalTimeStart;

	/**
	 * 预计到货时间结束
	 */
	@Schema(description="预计到货时间结束")
	private LocalDateTime estimatedArrivalTimeEnd;

	/**
	* 是否尾板提货：0 否，1 是
	*/
    @Schema(description="是否尾板提货：0 否，1 是")
    private Integer isTailgatePickup;

	/**
	 * 是否尾板卸货：0 否，1 是
	 */
	@Schema(description="是否尾板卸货：0 否，1 是")
	private Integer isTailgateUnloaded;

	/**
	 * 所属承运商
	 */
	@Schema(description="所属承运商")
	private Long carrierId;

	/**
	* 订单类型：1=托盘，2=包裹
	*/
    @Schema(description="订单类型：1=托盘，2=包裹")
    private Integer orderType;

	/**
	* 运输类型：1=整车运输，2=零担运输
	*/
    @Schema(description="运输类型：1=整车运输，2=零担运输")
    private Integer transportType;

	/**
	* 货物类型：1=普通货物，2=危险货物
	*/
    @Schema(description="货物类型：1=普通货物，2=危险货物")
    private Integer cargoType;

	/**
	* 地址类型：1=住宅-不需要尾板，2=住宅-需要尾板，3=商业地址-需要尾板
	*/
    @Schema(description="地址类型：1=住宅-不需要尾板，2=住宅-需要尾板，3=商业地址-需要尾板")
    private Integer addressType;

	/**
	 * 业务模式：1 揽收，2 中大件，3 卡派
	 */
	@Schema(description = "业务模式：1 揽收，2 中大件，3 卡派")
	private Integer businessModel;

	/**
	* 货品数量
	*/
    @Schema(description="货品数量")
    private Integer cargoQuantity;

	/**
	* 总重量(kg)
	*/
    @Schema(description="总重量(kg)")
    private BigDecimal totalWeight;

	/**
	* 总体积(m³)
	*/
    @Schema(description="总体积(m³)")
    private BigDecimal totalVolume;

	/**
	 * 提货证明（最多6张图片，逗号分割）
	 */
	@Schema(description="提货证明（最多6张图片，逗号分割）")
	private String pickupProof;

	/**
	 * 送货证明（最多6张图片，逗号分割）
	 */
	@Schema(description="送货证明（最多6张图片，逗号分割）")
	private String deliveryProof;

	/**
	* 运费合计：订单的总运费，包含基础运费、燃油费、税费、附加费等费用信息，系统自动计算
	*/
    @Schema(description="运费合计：订单的总运费，包含基础运费、燃油费、税费、附加费等费用信息，系统自动计算")
    private BigDecimal totalFreight;

	/**
	* 发货地经纬度
	*/
	@Schema(description="发货地经纬度")
	private String shipperLatLng;

	/**
	* 到货地经纬度
	*/
	@Schema(description="到货地经纬度")
	private String receiverLatLng;

	/**
	* 操作人
	*/
    @Schema(description="操作人")
    private String operator;

	/**
	 * 拒签日期
	 */
	@Schema(description="拒签日期")
	private LocalDateTime refuseTime;


	/**
	 * 拒签原因
	 */
	@Schema(description="拒签原因")
	private String refuseReasons;

	/**
	 * 是否扫描：0：未扫描/1:已扫描
	 */
	@Schema(description="是否扫描：0：未扫描/1:已扫描")
	private Integer isScan;

	/**
	 * 审核备注
	 */
	@Schema(description = "审核备注")
	private String auditRemark;

	/**
	 * 转单状态
	 */
	@Schema(description = "转单状态：0否，1是")
	private Integer transferStatus;

	/**
	 * 货物信息
	 */
	@TableField(exist = false)
	@Schema(description = "货物信息")
	private List<TmsCargoInfoEntity> cargoInfoEntityList;

	/**
	 * 附加服务
	 */
	@TableField(exist = false)
	@Schema(description = "附加服务")
	private List<TmsAdditionalServicesEntity> additionalServicesEntityList;

}
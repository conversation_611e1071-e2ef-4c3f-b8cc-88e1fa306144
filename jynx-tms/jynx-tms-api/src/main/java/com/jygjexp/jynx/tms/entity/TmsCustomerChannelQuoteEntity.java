package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 客户渠道报价
 *
 * <AUTHOR>
 * @date 2025-08-22 16:32:37
 */
@Data
@TenantTable
@TableName("tms_customer_channel_quote")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "客户渠道报价")
public class TmsCustomerChannelQuoteEntity extends Model<TmsCustomerChannelQuoteEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

	/**
	* 客户id
	*/
    @Schema(description="客户id")
    private Long customerId;

	/**
	* 服务商id
	*/
    @Schema(description="服务商id")
    private Long providerId;

	/**
	* 渠道id
	*/
    @Schema(description="渠道id")
    private Long channelId;

	/**
	* 报价id
	*/
    @Schema(description="报价id")
    private Long quoteId;

	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;


	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Long revision;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标志
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Long tenantId;
}
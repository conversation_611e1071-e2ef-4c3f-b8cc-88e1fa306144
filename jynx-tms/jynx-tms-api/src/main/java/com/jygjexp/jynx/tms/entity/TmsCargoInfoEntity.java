package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 卡派货物信息
 *
 * <AUTHOR>
 * @date 2025-03-05 19:02:02
 */
@Data
@TenantTable
@TableName("tms_cargo_info")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卡派货物信息")
public class TmsCargoInfoEntity extends BaseLogicEntity<TmsCargoInfoEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	 * 序号
	 */
	@Schema(description="序号")
	private String bagNum;

	/**
	* 长
	*/
    @Schema(description="长")
    private BigDecimal length;

	/**
	* 宽
	*/
    @Schema(description="宽")
    private BigDecimal width;

	/**
	* 高
	*/
    @Schema(description="高")
    private BigDecimal height;

	/**
	* 重量(kg)
	*/
    @Schema(description="重量(kg)")
    private BigDecimal weight;

	/**
	 * 复核重量（kg）
	 */
	@Schema(description = "复核重量（kg）")
	private BigDecimal reviewWeight;

	/**
	 * 复核体积（m³）
	 */
	@Schema(description = "复核体积（m³）")
	private BigDecimal reviewVolume;

	/**
	* 货物数量
	*/
    @Schema(description="货物数量")
    private Integer cargoQuantity;

	/**
	 * 委托单号
	 */
	@Schema(description="委托单号")
	private String entrustedOrderNumber;

	/**
	* 客户单号
	*/
    @Schema(description="客户单号")
    private String customerOrderNumber;


	/**
	* 箱号
	*/
    @Schema(description="箱号")
    private String boxNum;


	/**
	 * 货物描述信息
	 */
	private String cargoDescription;

	/**
	 * 单箱最大重量
	 */
	@Schema(description="单箱最大重量")
	private BigDecimal boxMaxWeight;

	/**
	 * 单包裹最大重量
	 */
	@Schema(description="单包裹最大重量")
	private BigDecimal pkgMaxWeight;

}
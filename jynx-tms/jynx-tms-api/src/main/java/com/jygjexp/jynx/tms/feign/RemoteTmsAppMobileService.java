package com.jygjexp.jynx.tms.feign;

import com.jygjexp.jynx.common.core.constant.ServiceNameConstants;
import com.jygjexp.jynx.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;

/**
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteTmsAppMobileService", value = ServiceNameConstants.APP_SERVER)
public interface RemoteTmsAppMobileService {

    /*
     * 	app短信发送；mobile：手机号、isTemplate：模版：1为默认登录，2为注册，3为默认密码
     *
     * */
    @GetMapping("/appmobile/{mobile}/{isTemplate}")
    R sendSmsCode(@PathVariable String mobile, @PathVariable Integer isTemplate);


    @PutMapping("/appmobile/{mobile}/{code}/{isAbroad}")
    R sendSms(@PathVariable String mobile, @PathVariable String code,@PathVariable Boolean isAbroad);

}

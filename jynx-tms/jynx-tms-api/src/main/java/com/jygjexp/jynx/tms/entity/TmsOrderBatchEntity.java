package com.jygjexp.jynx.tms.entity;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 批次管理
 *
 * <AUTHOR>
 * @date 2025-05-13 15:19:16
 */
@Data
@TenantTable
@ExcelIgnoreUnannotated
@TableName("tms_order_batch")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "批次管理")
public class TmsOrderBatchEntity extends BaseLogicEntity<TmsOrderBatchEntity> {


	/**
	* 批次id
	*/
    @TableId(type = IdType.AUTO)
	@ColumnWidth(20)
	@ExcelProperty("(serial number)序号")
    @Schema(description="批次id")
    private Long id;

	/**
	* 批次号
	*/
    @Schema(description="批次号")
	@ColumnWidth(20)
	@ExcelProperty("(batch number)批次号")
    private String batchNo;

	/**
	* 订单数
	*/
    @Schema(description="订单数")
	@ColumnWidth(20)
	@ExcelProperty("(order count)订单数")
    private Integer orderCount;

	/**
	* 区域数
	*/
    @Schema(description="区域数")
	@ColumnWidth(20)
	@ExcelProperty("(area quantity)区域数")
    private Integer areaCount;

	/**
	* 未送达数
	*/
    @Schema(description="未送达数")
	@ColumnWidth(30)
	@ExcelProperty("(Number of undelivered items)未送达数")
    private Integer nonDeliveryCount;

	/**
	* 已送达数
	*/
    @Schema(description="已送达数")
	@ColumnWidth(30)
	@ExcelProperty("(number of deliveries received)已送达数")
    private Integer deliveredCount;
	/**
	 * 批次时间
	 */
	@Schema(description="批次时间")
	@ColumnWidth(20)
	@ExcelProperty("(creation time)创建时间")
	private LocalDateTime batchTime;

	/**
	 * 是否开启扫描（0：否；1：是）
	 */
	@Schema(description="是否开启扫描（0：否；1：是）")
	@ColumnWidth(10)
	@ExcelProperty("(Enable scan)开启扫描")
	private Integer isScaning;

	/**
	 * 是否正向模板在使用批次(0: 否; 1: 是)
	 */
	@Schema(description="是否正向模板在使用批次(0: 否; 1: 是)")
	private Integer checkForwardUsing;
}
package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 物流商盲盒信息
 *
 * <AUTHOR>
 * @date 2025-07-18 18:00:37
 */
@Data
@TenantTable
@TableName("tms_blind_box")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "物流商盲盒信息")
public class TmsBlindBoxEntity extends Model<TmsBlindBoxEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 盲盒代码
	*/
    @Schema(description="盲盒代码")
    private String code;

	/**
	* 盲盒名称
	*/
    @Schema(description="盲盒名称")
    private String name;

	/**
	* 比价规则
	*/
    @Schema(description="比价规则")
    private Integer compareRule;

	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;

	/**
	 * 乐观锁
	 */
	@Schema(description="乐观锁")
	private Long revision;

	/**
	 * 备注
	 */
	@Schema(description="备注")
	private String remark;

	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="创建人")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="创建时间")
	private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	@Schema(description="更新人")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	@Schema(description="更新时间")
	private LocalDateTime updateTime;

	/**
	 * 删除标志
	 */
	@TableLogic
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="删除标志")
	private String delFlag;

	/**
	 * 租户号
	 */
	@Schema(description="租户号")
	private Long tenantId;

	/**
	 * 规则配置信息
	 */
	@TableField(exist = false)
	@Schema(description="规则配置信息")
	private List<TmsBlindBoxRuleEntity> rules;

}
package com.jygjexp.jynx.tms.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * 中大件、揽收订单状态
 */
public enum NewOrderStatus {
    /**
     * 待揽收
     */
    AWAITING_PICKUP(1, "AWAITING_PICKUP"),
    /**
     * 待发货
     */
    AWAITING_SHIPMENT(2, "AWAITING_SHIPMENT"),
    /**
     * 待运输
     */
    AWAITING_TRANSPORTATION(3, "AWAITING_TRANSPORTATION"),
    /**
     * 运输中
     */
    IN_TRANSIT(4, "IN_TRANSIT"),
    /**
     * 待收货
     */
    AWAITING_DELIVERY(5, "AWAITING_DELIVERY"),

    /**
     * 已完成
     */
    COMPLETED(6, "COMPLETED"),

    /**
     * 配送失败
     */
    FAILED_DELIVERY(10, "FAILED_DELIVERY"),

    /**
     * 待返仓
     */
    AWAITING_RETURN(7, "AWAITING_RETURN"),
    /**
     * 已返仓
     */
    RETURNED(8, "RETURNED"),
    /**
     * 再次派送
     */
    RETURN_TO_SEND(9, "RETURN_TO_SEND"),

    /**
     * 仓库收件
     */
    WAREHOUSE_RECEIVE(11, "WAREHOUSE_RECEIVE"),

    /**
     * 已拦截
     */
    BLOCK(23008, "BLOCK");

    NewOrderStatus(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    // 状态码
    private final Integer code;

    // 状态值
    private final String value;

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    // 用于快速查找的 Map
    private static final Map<Integer, NewOrderStatus> LOOKUP = new HashMap<>();

    // 静态初始化，将所有枚举项放入 Map 中
    static {
        for (NewOrderStatus status : EnumSet.allOf(NewOrderStatus.class)) {
            LOOKUP.put(status.code, status);
        }
    }

    /**
     * 根据 code 获取枚举项
     *
     * @param code 状态码
     * @return 对应的枚举项，如果不存在则返回 null
     */
    public static NewOrderStatus lookup(Integer code) {
        return LOOKUP.get(code);
    }

    /**
     * 通过状态字符串获取对应的数值（code）
     */
    public static Integer getCodeByName(String name) {
        for (NewOrderStatus status : NewOrderStatus.values()) {
            if (status.getValue().equalsIgnoreCase(name)) {
                return status.getCode();
            }
        }
        return 0;
    }
}

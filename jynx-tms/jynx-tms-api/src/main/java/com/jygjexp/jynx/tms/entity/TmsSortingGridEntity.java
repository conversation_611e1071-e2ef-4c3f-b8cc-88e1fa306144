package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 格口管理
 *
 * <AUTHOR>
 * @date 2025-04-23 18:42:57
 */
@Data
@TenantTable
@TableName("tms_sorting_grid")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "格口管理")
public class TmsSortingGridEntity extends BaseLogicEntity<TmsSortingGridEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 格口代码
	*/
    @Schema(description="格口代码")
    private String gridCode;

	/**
	* 格口名称
	*/
    @Schema(description="格口名称")
    private String gridName;

	/**
	 * 路线编号
	 */
	@Schema(description="路线编号")
	private String routeNumber;

	/**
	* 目的仓库ID
	*/
    @Schema(description="目的仓库ID")
    private Long warehouseId;

	/**
	* 格口状态（0：未启用，1:已启用）
	*/
    @Schema(description="格口状态（0：未启用，1:已启用）")
    private Integer isValid;


}
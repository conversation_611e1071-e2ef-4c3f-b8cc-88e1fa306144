package com.jygjexp.jynx.tms.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.EnumSet;

/**
 * <AUTHOR>
 */
@Getter
public enum ReceiveType {
    /**
     * 上门揽收
     */
    DOOR_PICKUP(1, "DOOR_PICKUP"),

    /**
     * 送货到仓
     */
    DELIVERY_TO_WAREHOUSE(2, "DELIVERY_TO_WAREHOUSE");

    private final Integer code;
    private final String value;

    ReceiveType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    // 循环变量
    private static final Map<Integer, ReceiveType> LOOKUP = new HashMap<>();

    // 静态初始化
    static {
        for (ReceiveType receiveType : EnumSet.allOf(ReceiveType.class)) {
            LOOKUP.put(receiveType.code, receiveType);
        }
    }

    /**
     * 根据code获取枚举项
     *
     * @param code
     * @return
     */
    public static ReceiveType lookup(Integer code) {
        return LOOKUP.get(code);
    }
}


package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户余额调账表
 *
 * <AUTHOR>
 * @date 2025-08-20 13:53:16
 */
@Data
@TenantTable
@TableName("tms_store_balance_customer_adjustment")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "客户余额调账表")
public class TmsStoreBalanceCustomerAdjustmentEntity extends Model<TmsStoreBalanceCustomerAdjustmentEntity> {


    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private Long storeCustomerId;

    /**
     * 调账类型(0: 上调 1:下调)
     */
    @Schema(description = "调账类型(0: 上调 1:下调)")
    private Integer adjustmentType;

    /**
     * 调账金额
     */
    @Schema(description = "调账金额")
    private BigDecimal adjustmentAmount;

    /**
     * 币种
     */
    @Schema(description = "币种")
    private String currencyCode;

    /**
     * 操作人
     */
    @Schema(description = "操作人")
    private String operator;

    /**
     * 消费记录id
     */
    @Schema(description = "消费记录id")
    private Long storeBalanceRecordId;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁")
    private Long revision;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标志
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "删除标志")
    private String delFlag;

    /**
     * 租户号
     */
    @Schema(description = "租户号")
    private Long tenantId;
}
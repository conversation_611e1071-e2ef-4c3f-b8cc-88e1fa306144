package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 附加费
 *
 * <AUTHOR>
 * @date 2025-07-14 15:12:06
 */
@Data
@TenantTable
@TableName("tms_fee_rule")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "附加费")
public class TmsFeeRuleEntity extends BaseLogicEntity<TmsFeeRuleEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	 * 费用代码
	 */
	@Schema(description="费用代码")
	private String feeCode;

	/**
	* 费用名称
	*/
    @Schema(description="费用名称")
    private String feeName;

	/**
	* 服务商ID
	*/
    @Schema(description="服务商ID")
    private Long providerId;

	/**
	* 费用类型，例如运输费、操作费等
	*/
    @Schema(description="费用类型")
    private Integer feeType;

	/**
	* 计算方式，例如按重量、按体积、固定金额等
	*/
    @Schema(description="计算方式（固定收费、特殊规则收费、百分比收费）")
    private Integer calculationMethod;

	/**
	* 计量单位，例如kg、m³、单次
	*/
    @Schema(description="计量单位（单票、单件）")
    private Integer unit;

	/**
	* 费用金额
	*/
    @Schema(description="费用金额")
    private BigDecimal amount;

	/**
	 * 配送费百分比
	 */
	@Schema(description="配送费百分比")
	private BigDecimal deliveryPercentage;

	/**
	* 版本号
	*/
    @Schema(description="版本号")
    private Integer version;

	/**
	* 原始费用规则ID（用于版本关联）
	*/
    @Schema(description="原始费用规则ID，用于关联同一费用规则的不同版本")
    private Long originalFeeRuleId;

	/**
	* 是否为当前生效版本：0：历史版本、1：当前版本
	*/
    @Schema(description="是否为当前生效版本：0：历史版本、1：当前版本")
    private Integer isActive;

	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;
}
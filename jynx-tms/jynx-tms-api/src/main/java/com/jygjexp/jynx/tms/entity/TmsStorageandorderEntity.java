package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 入库批次与订单记录表
 *
 * <AUTHOR>
 * @date 2025-04-16 14:15:18
 */
@Data
@TableName("tms_storageandorder")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "入库批次与订单记录表")
public class TmsStorageandorderEntity extends Model<TmsStorageandorderEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

	/**
	* 订单号
	*/
    @Schema(description="订单号")
    private String orderNo;

	/**
	* 入库批次号
	*/
    @Schema(description="入库批次号")
    private String storageOrderNo;

	/**
	* 出库批次号
	*/
    @Schema(description="出库批次号")
    private String outboundOrderNo;

	/**
	* 仓库ID
	*/
    @Schema(description="仓库ID")
    private Long warehouseId;

	/**
	* 仓库级别
	*/
    @Schema(description="仓库级别")
    private Integer warehouseType;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="创建时间")
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	@Schema(description="更新时间")
	private LocalDateTime updateTime;

	/**
	 * 删除标志
	 */
	@TableLogic
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="删除标志")
	private String delFlag;
}
package com.jygjexp.jynx.tms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 驿站
 *
 * <AUTHOR>
 * @date 2024-10-12 22:38:12
 */
@Data
@TenantTable
@TableName("tms_post")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "驿站")
public class TmsPostEntity extends Model<TmsPostEntity> {


    /**
     * ID
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description = "ID")
    private Integer postId;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;

    /**
     * 网点编号
     */
    @Schema(description = "网点编号")
    private String postNo;

    /**
     * 网点名称
     */
    @Schema(description = "网点名称")
    private String postName;

    /**
     * 管理方式：1=直营；2=加盟
     */
    @Schema(description = "管理方式：1=直营；2=加盟")
    private Integer postModel;

    /**
     * 国家
     */
    @Schema(description = "国家")
    private Integer countryId;

    /**
     * 省
     */
    @Schema(description = "省")
    private Integer provinceId;

    /**
     * 市
     */
    @Schema(description = "市")
    private Integer cityId;

    /**
     * 区域
     */
    @Schema(description = "区域")
    private Integer districtId;

    /**
     * 网点地址
     */
    @Schema(description = "网点地址")
    private String address;

    /**
     * 有效
     */
    @Schema(description = "有效")
    private Boolean isValid;

    /**
     * 最低补贴
     */
    @Schema(description = "最低补贴")
    private Integer subsidy;
    /**
     * 联系人
     */
    @Schema(description = "联系人")
    private String contact;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String contactMobile;

    /**
     * lng
     */
    @Schema(description = "lng")
    private Double lng;

    /**
     * lat
     */
    @Schema(description = "lat")
    private Double lat;

    /**
     * 当天包裹序列
     */
    @Schema(description = "当天包裹序列")
    private Integer todayIndex;

    /**
     * 取件码归零日期
     */
    @Schema(description = "取件码归零日期")
    private LocalDateTime takeCodeZeroDate;

    /**
     * 邮编
     */
    @Schema(description = "邮编")
    private String zip;

    /**
     * 有你自提码序列
     */
    @Schema(description = "有你自提码序列")
    private Integer youniOrderIndex;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createDate;

    /**
     * 所属仓库
     */
    @Schema(description = "所属仓库")
    private Integer warehouseId;

    /**
     * 今日大箱序号
     */
    @Schema(description = "今日大箱序号")
    private Integer packageIndex;

    /**
     * 所属分组
     */
    @Schema(description = "所属分组")
    private Integer groupId;

    /**
     * 营业时间
     */
    @Schema(description = "营业时间")
    private String openingHours;

    /**
     * demo店铺
     */
    @Schema(description = "demo店铺")
    private Integer isDemo;

    /**
     * 公司邮箱
     */
    @Schema(description = "公司邮箱")
    private String email;

    /**
     * 主营业务
     */
    @Schema(description = "主营业务")
    private String mainBizz;

    /**
     * 自提点业务营业时间
     */
    @Schema(description = "自提点业务营业时间")
    private String openingHoursPickup;

    /**
     * 营业场所面积
     */
    @Schema(description = "营业场所面积")
    private Double totalArea;

    /**
     * 存储面积
     */
    @Schema(description = "存储面积")
    private Double storageArea;

    /**
     * 业务联系人
     */
    @Schema(description = "业务联系人")
    private String bizzContact;

    /**
     * 业务联系人电话
     */
    @Schema(description = "业务联系人电话")
    private String bizzContactMobile;

    /**
     * 是否运营
     */
    @Schema(description = "是否运营")
    private Integer isOperation;

    /**
     * 合同签署日期
     */
    @Schema(description = "合同签署日期")
    private LocalDateTime signingDate;

    /**
     * 驿站存储及日均处理能力
     */
    @Schema(description = "驿站存储及日均处理能力")
    private String processingCapacity;

    /**
     * 友邻终端设备型号及数量
     */
    @Schema(description = "友邻终端设备型号及数量")
    private String deviceInfo;

    /**
     * 友邻其他设备名称及数量
     */
    @Schema(description = "友邻其他设备名称及数量")
    private String deviceInfoOther;

    /**
     * 支票抬头
     */
    @Schema(description = "支票抬头")
    private String chequePayable;

    /**
     * 银行转账账户
     */
    @Schema(description = "银行转账账户")
    private String bankAccount;

    /**
     * 电子转账邮箱
     */
    @Schema(description = "电子转账邮箱")
    private String transferEmail;

    /**
     * 转账方式：1=支票，2=银行转账，3=电子邮箱
     */
    @Schema(description = "转账方式：1=支票，2=银行转账，3=电子邮箱")
    private Integer transferType;

    /**
     * 退件金额（默认）
     */
    @Schema(description = "退件金额（默认）")
    private BigDecimal financeReturnPerAmount;

    /**
     * 退件金额（活动期）
     */
    @Schema(description = "退件金额（活动期）")
    private BigDecimal financeReturnPerAmountSpecial;

    /**
     * 退件活动期起
     */
    @Schema(description = "退件活动期起")
    private LocalDateTime financeReturnPerAmountStartTime;

    /**
     * 退件活动期止
     */
    @Schema(description = "退件活动期止")
    private LocalDateTime financeReturnPerAmountEndTime;

    /**
     * 店取金额（默认）
     */
    @Schema(description = "店取金额（默认）")
    private BigDecimal financePickupPerAmount;

    /**
     * 店取金额（活动期）
     */
    @Schema(description = "店取金额（活动期）")
    private BigDecimal financePickupPerAmountSpecial;

    /**
     * 店取活动期起
     */
    @Schema(description = "店取活动期起")
    private LocalDateTime financePickupPerAmountStartTime;

    /**
     * 店取活动期止
     */
    @Schema(description = "店取活动期止")
    private LocalDateTime financePickupPerAmountEndTime;

    /**
     * 店派金额（默认）
     */
    @Schema(description = "店派金额（默认）")
    private BigDecimal financeDeliveryPerAmount;

    /**
     * 店派金额（活动期）
     */
    @Schema(description = "店派金额（活动期）")
    private BigDecimal financeDeliveryPerAmountSpecial;

    /**
     * 店派活动期起
     */
    @Schema(description = "店派活动期起")
    private LocalDateTime financeDeliveryPerAmountStartTime;

    /**
     * 店派活动期止
     */
    @Schema(description = "店派活动期起")
    private LocalDateTime financeDeliveryPerAmountEndTime;

    /**
     * 地址描述
     */
    @Schema(description = "地址描述")
    private String addressDetail;

    /**
     * 质检仓库
     */
    @Schema(description = "质检仓库")
    private Boolean isWarehouse;

    /**
     * 默认司机
     */
    @Schema(description = "默认司机")
    private Integer defaultDriverId;

    /**
     * 是否pudo驿站
     */
    @Schema(description = "是否pudo驿站")
    private Boolean pudo;


    @Schema(description = "月账单")
    private Integer isMonthlyBill;


    @Schema(description = "距离")
    @TableField(exist = false)
    private Double distance;

    @Schema(description = "活动ID")
    private Integer activityId ;


    @Schema(description = "是否支持打印")
    private Boolean printFlag ;

    @Schema(description = "上级仓库id")
    private Long parentWarehouseId;


    /**
     * 关联照片
     */
    @TableField(exist = false)
    @Schema(description = "照片")
    private List<TmsPhotoEntity> photoList;


}
package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 笼车与订单绑定表
 *
 * <AUTHOR>
 * @date 2025-04-18 14:10:58
 */
@Data
@TableName("tms_cage_and_order")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "容器与订单绑定表")
public class TmsCageAndOrderEntity extends Model<TmsCageAndOrderEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 笼车编码
	*/
    @Schema(description="标签编码")
    private String cageCode;

	/**
	* 订单编号
	*/
    @Schema(description="订单编号")
    private String orderNo;

	/**
	* 绑定时间
	*/
    @Schema(description="绑定时间")
    private LocalDateTime bindTime;

	/*
	* 	绑定状态（0有效，1解绑）
	* */
	@Schema(description="绑定状态（0有效，1解绑）")
	private Integer status;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 删除标志
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志")
    private String delFlag;
}
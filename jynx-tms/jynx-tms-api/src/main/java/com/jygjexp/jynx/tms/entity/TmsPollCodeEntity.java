package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 卡派注册码
 *
 * <AUTHOR>
 * @date 2025-02-28 16:10:53
 */
@Data
@TenantTable
@TableName("tms_poll_code")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卡派注册码")
public class TmsPollCodeEntity extends BaseLogicEntity<TmsPollCodeEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 注册码
	*/
    @Schema(description="注册码")
    private String pollCode;

	/**
	* 承运商ID
	*/
    @Schema(description="承运商ID")
    private Long carrierId;

	/**
	* 有效期（天），示例值：7天
	*/
    @Schema(description="有效期（天），示例值：7天")
    private Integer validityDays;

	/**
	* 管理员用户ID
	*/
    @Schema(description="管理员用户ID")
    private Long adminUserId;

	/**
	* 启用状态：0-禁用 1-启用
	*/
    @Schema(description="启用状态：0-禁用 1-启用")
    private Integer isValid;


}
package com.jygjexp.jynx.tms.enums;

import lombok.Getter;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
public enum WarehouseType {

    /**
     * 一级
     */
    ONE_LEVEL(1, "One_Level"),

    /**
     * 二级
     */
    TWO_LEVEL(2, "TWO_LEVEL"),

    /**
     * 三级
     */
    THREE_LEVEL(3, "THREE_LEVEL");

    private final Integer code;
    private final String value;

    WarehouseType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    // 循环变量
    private static final Map<Integer, WarehouseType> LOOKUP = new HashMap<>();

    // 静态初始化
    static {
        for (WarehouseType warehouseType : EnumSet.allOf(WarehouseType.class)) {
            LOOKUP.put(warehouseType.code, warehouseType);
        }
    }

    /**
     * 根据code获取枚举项
     *
     * @param code
     * @return
     */
    public static WarehouseType lookup(Integer code) {
        return LOOKUP.get(code);
    }
}

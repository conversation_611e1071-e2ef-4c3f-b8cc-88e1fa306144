package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 云打印记录
 *
 * <AUTHOR>
 * @date 2025-03-19 15:17:08
 */
@Data
@TenantTable
@TableName("tms_print_record")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "云打印记录")
public class TmsPrintRecordEntity extends BaseLogicEntity<TmsPrintRecordEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 打印机id
	*/
    @Schema(description="打印机id")
    private Long printerId;

	/**
	* 打印任务id
	*/
    @Schema(description="打印任务id")
    private String title;

	/**
	* 打印地址
	*/
    @Schema(description="打印地址")
    private String url;

	/**
	* 打印状态
	*/
    @Schema(description="打印状态")
    private Integer status;

}
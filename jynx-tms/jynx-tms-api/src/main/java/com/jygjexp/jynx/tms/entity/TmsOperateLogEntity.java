package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * tms业务操作日志
 *
 * <AUTHOR>
 * @date 2025-07-21 17:33:43
 */
@Data
@TableName("tms_operate_log")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "tms业务操作日志")
public class TmsOperateLogEntity extends Model<TmsOperateLogEntity> {

 
	/**
	* id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private Long id;

	/**
	* 日志标题
	*/
    @Schema(description="日志标题")
    private String operateTitle;

	/**
	* 操作类型
	*/
    @Schema(description="操作类型")
    private String operateType;

	/**
	* 具体信息
	*/
    @Schema(description="具体信息")
    private String operateMsg;

	/**
	* 表名称
	*/
    @Schema(description="表名称")
    private String operateTableName;

	/**
	* 业务表主键id
	*/
    @Schema(description="业务表主键id")
    private Long serviceId;

	/**
	* 日志id
	*/
    @Schema(description="操作人id")
    private Long operatorId;

	/**
	* 操作时间
	*/
    @Schema(description="操作时间")
    private LocalDateTime operatorTime;

	@Schema(description="业务操作人id")
	private Long businessOperatorId;
}
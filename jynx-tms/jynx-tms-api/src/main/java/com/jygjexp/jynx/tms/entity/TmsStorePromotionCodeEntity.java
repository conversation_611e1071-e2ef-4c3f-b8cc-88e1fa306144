package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 推广码表
 *
 * <AUTHOR>
 * @date 2025-08-13 15:54:07
 */
@Data
@TenantTable
@TableName("tms_store_promotion_code")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "推广码表")
public class TmsStorePromotionCodeEntity extends Model<TmsStorePromotionCodeEntity> {


    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private Long id;

    /**
     * 推广人ID
     */
    @Schema(description = "推广人ID")
    private Long promoterId;

    /**
     * 优惠码
     */
    @Schema(description = "优惠码")
    private String code;

    /**
     * 服务商适用ID
     */
    @Schema(description = "服务商适用ID")
    private String providerIds;

//	/**
//	* 使用次数限制（NULL表示无限制）
//	*/
//    @Schema(description="使用次数限制（NULL表示无限制）")
//    private Integer useLimit;

    /**
     * 是否可重复使用
     */
    @Schema(description = "是否可重复使用 0:不可重复使用;1:可重复使用")
    private Integer repeatable;

    /**
     * 已使用次数
     */
    @Schema(description = "已使用次数")
    private Integer usedCount;

    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间")
    private LocalDate validStartTime;

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间")
    private LocalDate validEndTime;

    /**
     * 折扣类型：0、满减；1、打折
     */
    @Schema(description = "折扣类型：0、满减；1、打折")
    private Integer discountType;

    /**
     * 佣金类型：0、固定金额；1、固定比例
     */
    @Schema(description = "佣金类型：0、固定金额；1、固定比例")
    private Integer commissionType;

    /**
     * 佣金值（固定金额或比例）
     */
    @Schema(description = "佣金值（固定金额或比例）")
    private BigDecimal commissionValue;

    /**
     * 是否首次返回佣金：0、固定金额；1、比例
     */
    @Schema(description = "是否首次返回佣金：0、固定金额；1、比例")
    private Integer firstUseCommissionType;

    /**
     * 是否启用：0、未启用；1、启用
     */
    @Schema(description = "是否启用：0、未启用；1、启用")
    private Integer status;

    /**
     * 操作人
     */
    @Schema(description = "操作人")
    private String operator;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁")
    private Long revision;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标志
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "删除标志")
    private String delFlag;

    /**
     * 租户号
     */
    @Schema(description = "租户号")
    private Long tenantId;
}

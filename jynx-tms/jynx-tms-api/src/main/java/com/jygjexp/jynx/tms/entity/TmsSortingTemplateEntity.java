package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 分拣模版表
 *
 * <AUTHOR>
 * @date 2025-06-12 19:58:08
 */
@Data
@TenantTable
@TableName("tms_sorting_template")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "分拣模版表")
public class TmsSortingTemplateEntity extends Model<TmsSortingTemplateEntity> {


	/**
	* id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private Long id;

	/**
	* 模版名称
	*/
    @Schema(description="模版名称")
    private String templateName;

	/**
	* 模版代码
	*/
    @Schema(description="模版代码")
    private String templateCode;

	/**
	* 业务类型（1:揽收退件、2：正向派件）
	*/
    @Schema(description="业务类型（1:揽收退件;2：正向派件;5:半托管模板）")
    private Integer businessType;

	/**
	* 分拣规则类型（1：按商家、2：按路线号、3：按条件）
	*/
    @Schema(description="分拣规则类型（1：按商家、2：按路线号、3：按条件）")
    private Integer sortingRuleType;

	/**
	 * 是否启用
	 */
	@Schema(description="是否启用")
	private Boolean isEnable;

	/**
	* 状态
	*/
    @Schema(description="状态")
    private Integer status;

	/**
	* 站点id
	*/
    @Schema(description="站点id")
    private Integer siteId;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Integer revision;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 格口id
	*/
    @Schema(description="格口id")
    private Long gridId;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 逻辑删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="逻辑删除")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Integer tenantId;

}

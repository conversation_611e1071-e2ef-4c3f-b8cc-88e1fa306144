package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 路线规划主表
 *
 * <AUTHOR>
 * @date 2025-03-17 15:31:15
 */
@Data
@TableName("tms_route_plan")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "路线规划主表")
public class TmsRoutePlanEntity extends Model<TmsRoutePlanEntity> {


	/**
	* 路线规划id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="路线规划id")
    private Long routePlanId;

	/**
	 * 预先路线规划id
	 */
	@Schema(description="预先路线规划id")
	private Long preRoutePlanId;

	/**
	* 跳过的派送任务
	*/
    @Schema(description="跳过的派送任务")
    private String skippedShipments;

	/**
	* 路线规划成功的派送任务数量
	*/
    @Schema(description="路线规划成功的派送任务数量")
    private Integer performedShipmentCount;

	/**
	* 派送路程中持续时间，单位秒（s）
	*/
    @Schema(description="派送路程中持续时间，单位秒（s）")
    private String travelDuration;

	/**
	* 等待持续时间，单位秒（s）
	*/
    @Schema(description="等待持续时间，单位秒（s）")
    private String waitDuration;

	/**
	* 延误持续时间，单位秒（s）
	*/
    @Schema(description="延误持续时间，单位秒（s）")
    private String delayDuration;
 
	/**
	* breakDuration
	*/
    @Schema(description="breakDuration")
    private String breakDuration;

	/**
	* 派送持续时间（到达后派送持续）
	*/
    @Schema(description="派送持续时间（到达后派送持续）")
    private String visitDuration;

	/**
	* 线路总共持续时间
	*/
    @Schema(description="线路总共持续时间")
    private String totalDuration;

	/**
	* 线路总长，单位（米）
	*/
    @Schema(description="线路总长，单位（米）")
    private Long travelDistanceMeters;

	/**
	* 最大载重（本次路线的重量累加）
	*/
    @Schema(description="最大载重（本次路线的重量累加）")
    private BigDecimal totalLoad;

	/**
	* 规划跳过的派送任务数
	*/
    @Schema(description="规划跳过的派送任务数")
    private Integer skippedMandatoryShipmentCount;

	/**
	* 路线规划所用到的车辆数
	*/
    @Schema(description="路线规划所用到的车辆数")
    private Integer usedVehicleCount;

	/**
	* 最早车辆开始工作时间
	*/
    @Schema(description="最早车辆开始工作时间")
    private LocalDateTime earliestVehicleStartTime;

	/**
	* 最晚车辆开始工作时间
	*/
    @Schema(description="最晚车辆开始工作时间")
    private LocalDateTime latestVehicleEndTime;

	/**
	* 总共成本花费
	*/
    @Schema(description="总共成本花费")
    private BigDecimal totalCost;

	/**
	* 花费详情
	*/
    @Schema(description="花费详情")
    private String costs;
	/**
	 * 运输单号
	 */
	@Schema(description="运输单号")
	private String shipmentNo;

	/**
	 * 状态
	 */
	@Schema(description="状态")
	private Integer status;

	/**
	 * 站点id
	 */
	@Schema(description="站点id")
	private Integer siteId;

	/**
	 * 乐观锁
	 */
	@Schema(description="乐观锁")
	private Integer revision;

	/**
	 * 备注
	 */
	@Schema(description="备注")
	private String remark;

	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="创建人")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="创建时间")
	private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.UPDATE)
	@Schema(description="更新人")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.UPDATE)
	@Schema(description="更新时间")
	private LocalDateTime updateTime;

	/**
	 * 逻辑删除
	 */
	@TableLogic
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="逻辑删除")
	private String delFlag;

	/**
	 * 租户号
	 */
	@Schema(description="租户号")
	private Integer tenantId;
	/**
	 * 司机号
	 */
	@Schema(description="司机号")
	private String driverNo;
	/**
	 * 批次号
	 */
	@Schema(description="批次号")
	private String batchNo;
	/**
	 * 规划名称
	 */
	@Schema(description="规划名称")
	private String planName;
}
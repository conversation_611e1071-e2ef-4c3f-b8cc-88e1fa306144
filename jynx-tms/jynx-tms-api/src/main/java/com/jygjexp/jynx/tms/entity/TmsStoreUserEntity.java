package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门店用户绑定关系表
 *
 * <AUTHOR>
 * @date 2025-07-08 16:55:01
 */
@Data
@TableName("tms_store_user")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门店用户绑定关系表")
public class TmsStoreUserEntity extends Model<TmsStoreUserEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    private Long storeId;

	/**
	* 用户ID
	*/
    @Schema(description="用户ID")
    private Long userId;
}
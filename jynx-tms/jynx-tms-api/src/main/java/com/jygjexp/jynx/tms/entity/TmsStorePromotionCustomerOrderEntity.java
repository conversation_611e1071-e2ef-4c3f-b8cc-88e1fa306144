package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 订单优惠码绑定单
 *
 * <AUTHOR>
 * @date 2025-08-25 09:52:00
 */
@Data
@TenantTable
@TableName("tms_store_promotion_customer_order")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订单优惠码绑定单")
public class TmsStorePromotionCustomerOrderEntity extends Model<TmsStorePromotionCustomerOrderEntity> {


    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private Long id;

    /**
     * 订单号
     */
    @Schema(description="订单号")
    private String entrustedOrderNumber;

    /**
     * 客户id
     */
    @Schema(description="客户id")
    private Long storeCustomerId;

    /**
     * 推广码ID
     */
    @Schema(description="推广码ID")
    private Long promotionCodeId;

    /**
     * 推广码名称
     */
    @Schema(description="推广码名称")
    private String promotionCode;

    /**
     * 推广人id
     */
    @Schema(description = "推广人id")
    private Long promoterId;

    /**
     * 乐观锁
     */
    @Schema(description="乐观锁")
    private Integer revision;

    /**
     * 备注
     */
    @Schema(description="备注")
    private String remark;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记：0未删除，1已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标记：0未删除，1已删除")
    private String delFlag;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;
}

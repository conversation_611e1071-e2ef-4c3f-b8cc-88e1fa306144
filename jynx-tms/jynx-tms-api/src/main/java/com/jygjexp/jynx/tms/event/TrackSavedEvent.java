package com.jygjexp.jynx.tms.event;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 轨迹保存后的事件
 */
@Data
@AllArgsConstructor
public class TrackSavedEvent {
    // 订单号
    private String orderNo;
    // 需要推送的轨迹json
    private String trackText;
    /**
     * 是否推送 0=未推送，1=已推送
     */
    private Integer isPush;

    /**
     * 推送状态：成功，失败
     */
    private String pushStatus;
}

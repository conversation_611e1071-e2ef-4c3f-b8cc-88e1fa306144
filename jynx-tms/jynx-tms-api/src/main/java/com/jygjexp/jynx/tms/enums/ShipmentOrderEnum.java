package com.jygjexp.jynx.tms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: xiongpengfei
 * @Description: 卡派-运输单状态枚举
 * @Date: 2025/3/20 13:58
 */
@Getter
@AllArgsConstructor
public enum ShipmentOrderEnum {

    /**
     * 待运输
     */
    TO_BE_TRANSPORTED(0, "TO_BE_TRANSPORTED/待运输"),

    /**
     * 运输中
     */
    IN_TRANSIT(1, "PENDING_PICKUP/运输中"),

    /**
     * 已完成
     */
    COMPLETED(2, "COMPLETED/已完成"),

    /**
     * 已失败
     */
    FAILED(3, "FAILED/已失败");

    /**
     * 类型
     */
    private final Integer type;

    /**
     * 描述
     */
    private final String description;
}

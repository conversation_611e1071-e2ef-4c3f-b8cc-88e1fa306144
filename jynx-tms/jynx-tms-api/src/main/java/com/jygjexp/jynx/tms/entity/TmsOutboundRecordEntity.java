package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 卡派出库记录
 *
 * <AUTHOR>
 * @date 2025-04-03 14:22:15
 */
@Data
@TenantTable
@TableName("tms_outbound_record")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卡派出库记录")
public class TmsOutboundRecordEntity extends BaseLogicEntity<TmsOutboundRecordEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 仓库名称
	*/
    @Schema(description="仓库名称")
    private String warehouseName;
	/**
	 * 仓库id
	 */
	@Schema(description="仓库id")
	private Long warehouseId;

	/**
	* 仓库类型：0：一级，1：二级，3：三级，对应站点类型
	*/
    @Schema(description="仓库类型：0：一级，1：二级，3：三级，对应站点类型")
    private Integer warehouseType;

	/**
	* 出库单号(出库批次号)
	*/
    @Schema(description="出库单号(出库批次号)")
    private String outboundOrderNumber;
	/**
	 * 出库时间
	 */
	@Schema(description="出库时间")
	private LocalDateTime outboundTime;
	/**
	* 货品数量
	*/
    @Schema(description="货品数量")
    private Integer cargoQuantity;

	/**
	* 总重量(kg)
	*/
    @Schema(description="总重量(kg)")
    private BigDecimal totalWeight;

	/**
	* 总体积(m³)
	*/
    @Schema(description="总体积(m³)")
    private BigDecimal totalVolume;

	/**
	 * 是否派送
	 */
	@Schema(description="是否派送")
	private Integer isDispatch;

}
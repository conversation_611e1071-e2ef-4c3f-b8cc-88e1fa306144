package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 司机实时经纬度
 *
 * <AUTHOR>
 * @date 2025-03-21 13:51:32
 */
@Data
@TableName("tms_driver_location")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "司机实时经纬度")
public class TmsDriverLocationEntity extends Model<TmsDriverLocationEntity> {

 
	/**
	* driverId
	*/
    //@TableId(type = IdType.ASSIGN_ID)
	@TableId(type = IdType.INPUT) // 手动输入主键值
	@Schema(description="driverId")
    private Long driverId;
 
	/**
	* latitude
	*/
    @Schema(description="latitude")
    private BigDecimal latitude;
 
	/**
	* longitude
	*/
    @Schema(description="longitude")
    private BigDecimal longitude;
 
	/**
	* updateTime
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="updateTime")
    private LocalDateTime updateTime;
}
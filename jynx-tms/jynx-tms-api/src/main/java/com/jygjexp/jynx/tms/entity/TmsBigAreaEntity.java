package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 大区管理
 *
 * <AUTHOR>
 * @date 2025-05-09 14:06:03
 */
@Data
@TableName("tms_big_area")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "大区管理")
public class TmsBigAreaEntity extends Model<TmsBigAreaEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

	/**
	* 区域名称
	*/
    @Schema(description="地区名称")
    private String areaName;

	/**
	* 仓库ID
	*/
    @Schema(description="仓库ID")
    private Long warehouseId;

	/**
	* 数量
	*/
    @Schema(description="数量")
    private Integer num;



	/**
	* 分拣中心
	*/
	@TableField(exist = false)
    @Schema(description="分拣中心")
    private String warehouseName;



	/**
	* 区域
	*/
	@TableField(exist = false)
    @Schema(description="区域")
    private String areas;



	/**
	* 是否启用
	*/
    @Schema(description="是否启用")
    private Boolean isValid;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createDate;
}
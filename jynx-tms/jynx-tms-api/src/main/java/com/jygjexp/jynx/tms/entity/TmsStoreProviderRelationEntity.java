package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 门店快递订单供应商
 *
 * <AUTHOR>
 * @date 2025-07-16 20:24:32
 */
@Data
@TenantTable
@TableName("tms_store_provider_relation")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门店快递订单供应商")
public class TmsStoreProviderRelationEntity extends Model<TmsStoreProviderRelationEntity> {


    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private Long id;

    /**
     * 快递门店订单id
     */
    @Schema(description="主跟踪单号")
    private String mainEntrustedOrder;

    /**
     * 服务商代码
     */
    @Schema(description="服务商代码")
    private String providerCode;

    /**
     * 服务商名称
     */
    @Schema(description="服务商名称")
    private String providerName;

    /**
     * 盲盒代码
     */
    @Schema(description="盲盒代码")
    private String boxCode;

    /**
     * 盲盒名称
     */
    @Schema(description="盲盒名称")
    private String boxName;

    /**
     * 优惠码
     */
    @Schema(description = "优惠码")
    private String promotionCode;

    /**
     * 优惠金额
     */
    @Schema(description = "优惠金额")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal discountAmount;

    /**
     * 是否为推荐
     */
    @Schema(description = "是否推荐")
    private Integer defaultFlag;

    /**
     * 服务商方式
     */
    @Schema(description="服务商方式")
    private String providerServiceWay;

    /**
     * 运输时效
     */
    @Schema(description="运输时效")
    private String providerTransportTime;

    /**
     * 运费$
     */
    @Schema(description="运费$")
    private BigDecimal freightAmount;

    /**
     * 底价$
     */
    @Schema(description="底价$")
    private BigDecimal baseFreightAmount;

    /**
     * 利润率
     */
    @Schema(description = "利润率")
    private BigDecimal profitRate;

    /**
     * 保费$
     */
    @Schema(description="保费$")
    private BigDecimal insuranceAmount;

    /**
     * POD/面签费$
     */
    @Schema(description="POD/面签费$")
    private BigDecimal podAmount;

    /**
     * 税费$
     */
    @Schema(description="税费$")
    private BigDecimal taxAmount;

    /**
     * 乐观锁
     */
    @Schema(description="乐观锁")
    private Integer revision;

    /**
     * 备注
     */
    @Schema(description="备注")
    private String remark;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记：0未删除，1已删除
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标记：0未删除，1已删除")
    private String delFlag;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;
}

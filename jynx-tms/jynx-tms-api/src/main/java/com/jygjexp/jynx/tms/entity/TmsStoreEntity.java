package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;

import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * 门店信息表
 *
 * <AUTHOR>
 * @date 2025-07-09 11:33:45
 */
@Data
@TenantTable
@TableName("tms_store")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门店信息表")
public class TmsStoreEntity extends Model<TmsStoreEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	 * 管理员用户ID
	 */
	@Schema(description = "管理员用户ID")
	private Long userId;

	/**
	* 门店代码
	*/
    @Schema(description="门店代码")
    private String storeCode;

	/**
	* 门店名称
	*/
    @Schema(description="门店名称")
    private String storeName;

	/**
	* 联系人
	*/
	@Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9_]*$", message = "用户名必须以字母开头，只包含字母、数字和下划线")
    @Schema(description="联系人")
    private String contactName;

	/**
	* 联系人电话
	*/
    @Schema(description="联系人电话")
    private String contactPhone;

	/**
	* 门店地址
	*/
    @Schema(description="门店地址")
    private String storeAddress;

	/**
	* 地区
	*/
    @Schema(description="地区")
    private String region;

	/**
	 * 邮编
	 */
	@Schema(description = "邮编")
	private String postalCode;

	/**
	 * 门店经纬度
	 */
	@Schema(description = "门店经纬度")
	private String storeLatLng;

	/**
	* 门店类型 0-自营 1-加盟
	*/
    @Schema(description="门店类型 0-自营 1-加盟")
    private Integer storeType;

	/**
	* 结算方式 0-月结 1-其他
	*/
    @Schema(description="结算方式 0-月结 1-其他")
    private Integer settlementType;

	/**
	* 支付方式 0-银行支付 1-其他
	*/
    @Schema(description="支付方式 0-银行支付 1-其他")
    private Integer payType;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	 * 是否启用：0、未启用；1、启用
	 */
	@Schema(description = "是否启用：0、未启用；1、启用")
	private Integer status;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Long revision;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标志
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Long tenantId;
}

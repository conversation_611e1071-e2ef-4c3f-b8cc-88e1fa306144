package com.jygjexp.jynx.tms.entity;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.Map;

/**
 * 渠道管理
 *
 * <AUTHOR>
 * @date 2025-08-22 07:05:32
 */
@Data
@TableName("tms_channel")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "渠道管理")
public class TmsChannel extends BaseLogicEntity<TmsChannel> {


    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 渠道代码
     */
    @Schema(description = "渠道代码")
    private String channelCode;

    /**
     * 渠道名称
     */
    @Schema(description = "渠道名称")
    private String channelName;

    /**
     * 渠道类型 0:中大件配送
     */
    @Schema(description = "渠道类型 0:中大件配送")
    private Integer channelType;

    /**
     * 货物属性
     */
    @Schema(description = "货物属性 0:普通货物;1:危险货物")
    private Integer goodsType;

    /**
     * 时效
     */
    @Schema(description = "时效")
    private Integer timeliness;

    /**
     * 材积除
     */
    @Schema(description = "材积除")
    private Integer volumetricWeight;

    /**
     * 计费节点 1:固定
     */
    @Schema(description = "计费节点 1:包裹入仓\\分拣")
    private Integer billingNode;

    /**
     * 服务商
     */
    @Schema(description = "服务商")
    private String serviceProvider;

    /**
     * 可选分区
     */
    @Schema(description = "可选分区")
    private String reachableArea;

    /**
     * 不可达分区
     */
    @Schema(description = "不可达分区")
    private String unreachableArea;

    /**
     * 状态
     */
    @Schema(description = "状态 0:禁用;1:启用")
    private Integer status;
}
package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.github.yulichang.annotation.EntityMapping;
import java.util.List;

/**
 * 包裹基础运费表-模版
 *
 * <AUTHOR>
 * @date 2025-03-07 14:20:31
 */
@Data
@TenantTable
@TableName("tms_basic_freight_pkg")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "包裹基础运费表-模版")
public class TmsBasicFreightPkgEntity extends BaseLogicEntity<TmsBasicFreightPkgEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键ID")
    private Long pkgFreId;

	/**
	* 费用模版名称
	*/
    @Schema(description="费用模版名称")
    private String pkgFreName;

	/**
	* 计费方式
	*/
    @Schema(description="计费方式")
    private Integer pkgFreType;

	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;

}
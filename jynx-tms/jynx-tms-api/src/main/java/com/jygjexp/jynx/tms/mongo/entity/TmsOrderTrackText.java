package com.jygjexp.jynx.tms.mongo.entity;

import com.mongoplus.annotation.ID;
import com.mongoplus.annotation.collection.CollectionName;
import com.mongoplus.enums.IdTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description = "订单轨迹")
@CollectionName(value = "order_tracking")
public class TmsOrderTrackText {

    @ID(type = IdTypeEnum.ASSIGN_ID)
    private String id;
    private String orderNo;
    private String tracking;
    private Date trackingTime;

}
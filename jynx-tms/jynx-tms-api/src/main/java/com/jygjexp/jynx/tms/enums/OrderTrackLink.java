package com.jygjexp.jynx.tms.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * 中大件轨迹节点触发环节
 */
public enum OrderTrackLink {
    /**
     * 客户下单
     */
    ORDER_RECEIVED(1, "客户下单"),
    /**
     * 司机已揽货
     */
    AWAITING_TRANSPORTATION(2, "司机已揽货"),
    /**
     * 二级仓已接货
     */
    TWO_COLLECTION_WAREHOUSE_RECEIVED(3, "二级仓已接货"),
    /**
     * 货物发往分拣中心
     */
    SEND_SORTING_CENTER(4, "货物发往分拣中心"),
    /**
     * 分拣中心已接货
     */
    WAREHOUSE_RECEIVED(5, "分拣中心已接货"),
    /**
     * 分拣中心已分拣
     */
    WAREHOUSE_SORTING(6, "分拣中心已分拣"),

    /**
     * 货物发往归属二级仓
     */
    GATEWAY_TRANSIT(7, "货物发往归属二级仓"),

    /**
     * 货物到达归属二级仓
     */
    TWO_WAREHOUSE_RECEIVED(8, "货物到达归属二级仓"),
    /**
     * 司机扫描货物
     */
    PARCEL_SCANNED(9, "司机扫描货物"),
    /**
     * 派送中
     */
    IN_TRANSIT(10, "派送中"),

    /**
     * 派送完成
     */
    DELIVERED(11, "派送完成"),

    /**
     * 首次配送失败
     */
    FAILED_DELIVERY_1ST(12, "首次配送失败"),

    /**
     * 第二次配送失败
     */
    FAILED_DELIVERY_2ND(13, "第二次配送失败"),

    /**
     * 配送失败，正在返回总仓
     */
    FAILED_DELIVERY_RETURN_WAREHOURE(14, "配送失败，正在返回总仓"),

    /**
     * 返回总仓
     */
    PARCEL_RETURNED_SORTING(15, "返回总仓"),

    /**
     * 包裹上架
     */
    SELF_PICKUP(16, "包裹上架"),

    /**
     * 自提
     */
    PICKED_UD(17, "自提"),

    /**
     * 退回寄件方
     */
    RETURN_SENDER(18, "退回寄件方"),

    /**
     * 赔付
     */
    COMPENSATION_PAID(19, "赔付");

    OrderTrackLink(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    // 状态码
    private final Integer code;

    // 状态值
    private final String value;

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    // 用于快速查找的 Map
    private static final Map<Integer, OrderTrackLink> LOOKUP = new HashMap<>();

    // 静态初始化，将所有枚举项放入 Map 中
    static {
        for (OrderTrackLink status : EnumSet.allOf(OrderTrackLink.class)) {
            LOOKUP.put(status.code, status);
        }
    }

    /**
     * 根据 code 获取枚举项
     *
     * @param code 状态码
     * @return 对应的枚举项，如果不存在则返回 null
     */
    public static OrderTrackLink lookup(Integer code) {
        return LOOKUP.get(code);
    }

    /**
     * 通过状态字符串获取对应的数值（code）
     */
    public static Integer getCodeByName(String name) {
        for (OrderTrackLink status : OrderTrackLink.values()) {
            if (status.getValue().equalsIgnoreCase(name)) {
                return status.getCode();
            }
        }
        return 0;
    }
}

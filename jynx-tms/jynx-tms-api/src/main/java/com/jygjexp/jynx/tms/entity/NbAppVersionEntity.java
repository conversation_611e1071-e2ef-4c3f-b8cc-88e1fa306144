package com.jygjexp.jynx.tms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * app版本管理
 *
 * <AUTHOR>
 * @date 2024-11-12 00:27:59
 */
@Data
@TenantTable
@TableName("nb_app_version")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "app版本管理")
public class NbAppVersionEntity extends Model<NbAppVersionEntity> {


    /**
     * ID
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description="ID")
    private Integer appId;

    /**
     * 版本
     */
    @Schema(description="版本")
    private String versionName;

    /**
     * 发布时间
     */
    @Schema(description="发布时间")
    private Date createDate;

    /**
     * 强制更新
     */
    @Schema(description="强制更新")
    private Integer isFocus;

    /**
     * 描述
     */
    @Schema(description="描述")
    private String appDesc;

    /**
     * 下载地址
     */
    @Schema(description="下载地址")
    private String downloadUrl;

    /**
     * 有效
     */
    @Schema(description="有效")
    private Integer isValid;

    /**
     * versionCode
     */
    @Schema(description="versionCode")
    private Integer versionCode;

    /**
     * 灰度发布
     */
    @Schema(description="灰度发布")
    private Boolean isGaryRelease;

    /**
     * 设备类型：1PDA，2手机
     */
    @Schema(description="设备类型：1PDA，2手机")
    private Integer deviceType;

    /**
     * app类型：1司机APP，2驿站APP
     */
    @Schema(description="app类型：1司机APP，2驿站APP，3中大件APP")
    private Integer appType;

    /**
     * 是否启用ios更新，0 否 1 是
     */
    @Schema(description="是否启用ios更新，0 否 1 是")
    private Boolean isIos;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;
}
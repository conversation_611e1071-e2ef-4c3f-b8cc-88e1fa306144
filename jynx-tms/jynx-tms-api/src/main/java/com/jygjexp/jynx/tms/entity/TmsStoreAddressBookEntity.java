package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 门店地址簿
 *
 * <AUTHOR>
 * @date 2025-07-14 17:42:38
 */
@Data
@TenantTable
@TableName("tms_store_address_book")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门店地址簿")
public class TmsStoreAddressBookEntity extends Model<TmsStoreAddressBookEntity> {


    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键ID")
    private Long id;

    /**
     * 门店客户id
     */
    @Schema(description="门店客户id")
    private Long storeCustomerId;

    /**
     * 联系人
     */
    @Schema(description="联系人")
    private String contacts;

    /**
     * 联系电话
     */
    @Schema(description="联系电话")
    private String phone;

    /**
     * 国家/地区
     */
    @Schema(description="国家/地区")
    private String country;

    /**
     * 省/州
     */
    @Schema(description="省/州")
    private String province;

    /**
     * 城市
     */
    @Schema(description="城市")
    private String city;

    /**
     * 邮政编码
     */
    @Schema(description="邮政编码")
    private String postalCode;

    /**
     * 业务类型：0快递
     */
    @Schema(description="业务类型：0快递")
    private Integer type;

    /**
     * 地址类型：0发货 1:收件
     */
    @Schema(description="地址类型：0发货 1:收件")
    private Integer subType;

    /**
     * 详细地址
     */
    @Schema(description="详细地址")
    private String address;

    /**
     * 乐观锁
     */
    @Schema(description="乐观锁")
    private Integer revision;

    /**
     * 备注
     */
    @Schema(description="备注")
    private String remark;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记：0未删除，1已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标记：0未删除，1已删除")
    private String delFlag;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;
}

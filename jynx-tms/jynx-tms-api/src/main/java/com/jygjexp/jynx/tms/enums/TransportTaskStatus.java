package com.jygjexp.jynx.tms.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: chenchang
 * @Description: 中大件任务状态
 * @Date: 2025/4/7 20:13
 */
public enum TransportTaskStatus {
    /**
     * 待提货
     */
    PENDING_PICKUP(25001, "PENDING_PICKUP"),

    /**
     * 运输中
     */
    IN_TRANSIT(25002, "IN_TRANSIT"),

    /**
     * 已完成
     */
    DELIVERED(25003, "DELIVERED"),

    /**
     * 已取消
     */
    CANCELLED(25004, "CANCELLED");


    TransportTaskStatus(Integer code, String value) {
        this.code = code;
        this.value = value;
    }


    //状态
    private final Integer code;

    //值
    private final String value;

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    //循环变量
    private static final Map<Integer, TransportTaskStatus> LOOKUP = new HashMap<>();

    // 静态初始化
    static {
        for (TransportTaskStatus orderEnum : EnumSet.allOf(TransportTaskStatus.class)) {
            LOOKUP.put(orderEnum.code, orderEnum);
        }
    }

    /**
     * 根据code获取枚举项
     *
     * @param code
     * @return
     */
    public static TransportTaskStatus lookup(Integer code) {
        return LOOKUP.get(code);
    }

}

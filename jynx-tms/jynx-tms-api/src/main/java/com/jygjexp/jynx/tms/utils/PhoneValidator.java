package com.jygjexp.jynx.tms.utils;

import cn.hutool.core.lang.Validator;

public class PhoneValidator {

    // 中国手机号正则（+86 或 86 开头，11 位数字）
    private static final String CHINA_MOBILE_REGEX = "^(\\+?86)?1[3-9]\\d{9}$";

    // 加拿大手机号正则（+1 开头，10 位数字，格式如 +1XXXYYYZZZZ）
    private static final String CANADA_MOBILE_REGEX = "^(\\+?1)?[2-9]\\d{9}$";

    public static boolean validatePhone(String phone) {
        boolean isValidChina = Validator.isMatchRegex(CHINA_MOBILE_REGEX, phone);
        boolean isValidCanada = Validator.isMatchRegex(CANADA_MOBILE_REGEX, phone);
        if (!isValidChina && !isValidCanada) {
           return false;
        }
        return true;
    }

    public static boolean validateChinaMobile(String phone) {
       return Validator.isMatchRegex(CHINA_MOBILE_REGEX, phone);
    }


    public static boolean validateCanadaMobile(String phone) {
        return Validator.isMatchRegex(CANADA_MOBILE_REGEX, phone);
    }
}

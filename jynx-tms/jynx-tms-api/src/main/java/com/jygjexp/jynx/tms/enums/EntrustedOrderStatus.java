package com.jygjexp.jynx.tms.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: chenchang
 * @Description: 委托订单状态枚举
 * @Date: 2025/3/12 16:09
 */
public enum EntrustedOrderStatus {
    /**
     * 待审批
     */
    PENDING_APPROVAL(24001, "PENDING_APPROVAL"),
    /**
     * 待分配
     */
    PENDING_ALLOCATION(24002, "PENDING_ALLOCATION"),
    /**
     * 已驳回
     */
    REJECTED(24003, "REJECTED"),
    /**
     * 待提货
     */
    PENDING_PICKUP(24004, "PENDING_PICKUP"),
    /**
     * 运输中
     */
    IN_TRANSIT(24005, "IN_TRANSIT"),
    /**
     * 已完成
     */
    DELIVERED(24006, "DELIVERED"),
    /**
     * 未完成(已失败)
     */
    INCOMPLETE(24007, "INCOMPLETE"),

    /**
     *待二次提货状态
     */
    PENDING_SECOND_PICKUP(24008,"PENDING_SECOND_PICKUP"),
    /**
     *待二次派送状态
     */
    WAIT_SECOND_DELIVERY(24009,"WAIT_SECOND_DELIVERY"),
    /**
         *异常状态
     */
    DELIVERY_EXCEPTION(24010,"DELIVERY_EXCEPTION");

    EntrustedOrderStatus(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    //状态
    private final Integer code;

    //值
    private final String value;

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    //循环变量
    private static final Map<Integer, EntrustedOrderStatus> LOOKUP = new HashMap<>();

    // 静态初始化
    static {
        for (EntrustedOrderStatus orderEnum : EnumSet.allOf(EntrustedOrderStatus.class)) {
            LOOKUP.put(orderEnum.code, orderEnum);
        }
    }

    /**
     * 根据code获取枚举项
     *
     * @param code
     * @return
     */
    public static EntrustedOrderStatus lookup(Integer code) {
        return LOOKUP.get(code);
    }

}

package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jygjexp.jynx.common.core.util.TenantTable;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 卡派货架信息表
 *
 * <AUTHOR>
 * @date 2025-02-11 17:28:53
 */
@Data
@TenantTable
@TableName("tms_shelf")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卡派货架信息表")
public class TmsShelfEntity extends BaseLogicEntity<TmsPickupEntity> {


	/**
	* 货架主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="货架主键")
    private Long shelfId;

	/**
	* 货架代码
	*/
    @Schema(description="货架代码")
    private String shelfCode;

	/**
	* 海外仓编码
	*/
    @Schema(description="海外仓编码")
    private String warehouseCode;

	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;

	/**
	* 当前包裹数量
	*/
    @Schema(description="当前包裹数量")
    private Integer pkgNumber;

}
package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 覆盖区域
 *
 * <AUTHOR>
 * @date 2025-04-07 20:31:03
 */
@Data
@TableName("tms_over_area")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "覆盖区域")
public class TmsOverAreaEntity extends Model<TmsOverAreaEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

	/**
	* 区域名称
	*/
    @Schema(description="区域名称")
    private String name;

	/**
	* 仓库名称
	*/
    @Schema(description="仓库名称")
    private String warehouseName;

	/**
	* 仓库名称
	*/
    @Schema(description="仓库ID")
    private Long warehouseId;

	/**
	* 颜色
	*/
    @Schema(description="颜色")
    private String color;

	/**
	* 仓库类型
	*/
    @Schema(description="仓库类型")
    private Integer warehouseType;

	/**
	* 路线编号
	*/
    @Schema(description="路线编号")
    private String routeNumber;

	/**
	* 面单代码
	*/
    @Schema(description="面单代码")
    private String labelCode;

	/**
	* 覆盖邮编
	*/
    @Schema(description="覆盖邮编")
    private String zip;


	/**
	* 所属大区
	*/
    @Schema(description="所属大区")
    private Long bigAreaId;

	/**
	* 数量
	*/
    @Schema(description="数量")
    private Integer num;

	/**
	* 是否有效
	*/
    @Schema(description="是否有效")
    private Integer isValid;
}
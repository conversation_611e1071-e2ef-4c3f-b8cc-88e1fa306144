package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 司机扫描订单记录表
 *
 * <AUTHOR>
 * @date 2025-07-03 14:25:12
 */
@Data
@TenantTable
@TableName("tms_order_scan_record")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "司机扫描订单记录表")
public class TmsOrderScanRecordEntity extends Model<TmsOrderScanRecordEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 订单号
	*/
    @Schema(description="订单号")
    private String orderNo;

	/**
	* 司机ID
	*/
    @Schema(description="司机ID")
    private Long driverId;

	/**
	* 司机姓名
	*/
    @Schema(description="司机姓名")
    private String driverName;

	/**
	* 司机编号
	*/
    @Schema(description="司机编号")
    private String driverNum;

	/**
	 * 扫描类型（1、揽收；2、干线；3、派送）
	 */
	@Schema(description = "扫描类型（1、揽收；2、干线；3、派送）")
	private Integer scanType;

	/**
	* 扫描时间
	*/
    @Schema(description="扫描时间")
    private LocalDateTime scanTime;

	/**
	* 状态
	*/
    @Schema(description="状态")
    private Integer status;

	/**
	* 站点id
	*/
    @Schema(description="站点id")
    private Integer siteId;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Integer revision;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 逻辑删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="逻辑删除")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Integer tenantId;
}
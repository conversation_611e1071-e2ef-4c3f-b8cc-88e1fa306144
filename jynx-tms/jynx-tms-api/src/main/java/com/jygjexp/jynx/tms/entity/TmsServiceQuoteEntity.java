package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 服务商报价表
 *
 * <AUTHOR>
 * @date 2025-07-09 17:49:14
 */
@Data
@TenantTable
@TableName("tms_service_quote")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "服务商报价表")
public class TmsServiceQuoteEntity extends BaseLogicEntity<TmsServiceQuoteEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 报价代码
	*/
    @Schema(description="报价代码")
    private String quoteCode;

	/**
	* 报价名称
	*/
    @Schema(description="报价名称")
    private String quoteName;

	/**
	* 服务商ID
	*/
    @Schema(description="服务商ID")
    private Long providerId;

	/**
	* 可达分区ID
	*/
    @Schema(description="可达分区ID")
    private Long reachableRegionId;

	/**
	* 不可达分区ID
	*/
    @Schema(description="不可达分区ID")
    private Long unreachableRegionId;

	/**
	 * 渠道ID
	 */
	@Schema(description="渠道ID")
	private Long channelId;


	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;

}
package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 中大件入库记录
 *
 * <AUTHOR>
 * @date 2025-04-02 20:50:51
 */
@Data
@TenantTable
@TableName("tms_storage_record")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卡派入库记录")
public class TmsStorageRecordEntity extends BaseLogicEntity<TmsStorageRecordEntity> {


	/**
	 * 主键ID
	 */
	@TableId(type = IdType.AUTO)
	@Schema(description = "主键ID")
	private Long id;

	/**
	 * 仓库名称
	 */
	@Schema(description = "仓库id")
	private Long warehouseId;

	/**
	 * 仓库类型：0：一级，1：二级，3：三级，对应站点类型
	 */
	@Schema(description = "仓库类型：0：一级，1：二级，3：三级，对应站点类型")
	private Integer warehouseType;

	/**
	 * 入库单号
	 */
	@Schema(description = "入库批次号")
	private String storageOrderNumber;

	/**
	 * 入库时间
	 */
	@Schema(description = "入库时间")
	private LocalDateTime storageTime;


	/**
	 * 货品数量
	 */
	@Schema(description = "货品总数量")
	private Integer cargoQuantity;

	/**
	 * 总重量(kg)
	 */
	@Schema(description = "总重量(kg)")
	private BigDecimal totalWeight;

	/**
	 * 总体积(m³)
	 */
	@Schema(description = "总体积(m³)")
	private BigDecimal totalVolume;


}
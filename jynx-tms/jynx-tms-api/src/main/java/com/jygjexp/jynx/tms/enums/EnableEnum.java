package com.jygjexp.jynx.tms.enums;

import lombok.Getter;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
public enum EnableEnum {

    /**
     * 启用
     */
    ENABLE(1, "enable"),

    /**
     * 禁用
     */
    DISABLE(0, "disable");

    private final Integer code;
    private final String value;

    EnableEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    // 循环变量
    private static final Map<Integer, EnableEnum> LOOKUP = new HashMap<>();

    // 静态初始化
    static {
        for (EnableEnum enableEnum : EnumSet.allOf(EnableEnum.class)) {
            LOOKUP.put(enableEnum.code, enableEnum);
        }
    }

    /**
     * 根据code获取枚举项
     *
     * @param code
     * @return
     */
    public static EnableEnum lookup(Integer code) {
        return LOOKUP.get(code);
    }
}
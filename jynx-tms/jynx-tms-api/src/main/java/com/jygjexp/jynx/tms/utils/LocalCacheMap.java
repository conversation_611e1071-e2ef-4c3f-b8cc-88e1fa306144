package com.jygjexp.jynx.tms.utils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

public class LocalCacheMap {

    // 使用ConcurrentHashMap来保证线程安全
    private static final Map<String, Object> cache = new ConcurrentHashMap<>();
    // 最大容量，防止内存占用过多
    private static final int MAX_SIZE = 5000;
    // 锁对象，防止并发情况下清理时出问题
    private static final ReentrantLock lock = new ReentrantLock();

    // 私有化构造函数，确保只有一个实例
    private LocalCacheMap() {}

    // 单例实现
    private static class CacheHolder {
        private static final LocalCacheMap INSTANCE = new LocalCacheMap();
    }

    public static LocalCacheMap getInstance() {
        return CacheHolder.INSTANCE;
    }

    // 获取值的方法
    public Object get(String key) {
        return cache.get(key);
    }

    // 设置值的方法
    public void set(String key, Object value) {
        // 如果缓存超出了最大容量，进行清理
        if (cache.size() >= MAX_SIZE) {
            cleanUp();
        }
        cache.put(key, value);
    }

    // 移除键值对
    public void remove(String key) {
        cache.remove(key);
    }

    // 清理旧的数据，简单的LRU策略
    private void cleanUp() {
        // 获取锁，保证清理过程中的线程安全
        lock.lock();
        try {
            cache.clear();
        } finally {
            lock.unlock();
        }
    }
}

package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jygjexp.jynx.common.core.util.TenantTable;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 卡派自提信息表
 *
 * <AUTHOR>
 * @date 2025-02-11 15:31:30
 */
@Data
@TenantTable
@TableName("tms_pickup")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卡派自提信息表")
public class TmsPickupEntity extends BaseLogicEntity<TmsPickupEntity> {


	/**
	* 自提主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="自提主键")
    private Long pickupId;

	/**
	* 取件码
	*/
    @Schema(description="取件码")
    private String pickupCode;

	/**
	* 包裹单号
	*/
    @Schema(description="包裹单号")
    private String pkgNo;

	/**
	* 海外仓编码
	*/
    @Schema(description="海外仓编码")
    private String warehouseCode;

	/**
	* 货架编码
	*/
    @Schema(description="货架编码")
    private String shelfCode;

	/**
	* 自提状态：0：待自提、1：待处理、2：已处理、3：已下架:4：已自提
	*/
    @Schema(description="自提状态：0：待自提、1：待处理、2：已处理、3：已下架:4：已自提")
    private Integer pickupStatus;

	/**
	* 上架天数
	*/
    @Schema(description="上架天数")
    private Integer shelfDay;

	/**
	* 客户预约取件时间
	*/
    @Schema(description="客户预约取件时间")
    private LocalDateTime pickupTime;

	/**
	* 自提收件人
	*/
    @Schema(description="自提收件人")
    private String pickupName;

	/**
	* 自提收件人电话
	*/
    @Schema(description="自提收件人电话")
    private String pickupPhone;

	/**
	* 上架时间
	*/
    @Schema(description="上架时间")
    private LocalDateTime shelfTime;

	/**
	* 处理结果
	*/
    @Schema(description="处理结果")
    private String remark;


}
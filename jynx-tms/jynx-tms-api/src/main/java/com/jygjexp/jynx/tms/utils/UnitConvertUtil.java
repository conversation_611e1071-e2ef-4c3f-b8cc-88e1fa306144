package com.jygjexp.jynx.tms.utils;

import com.jygjexp.jynx.tms.constants.StoreConstants;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 单位换算工具
 */
public class UnitConvertUtil {
    // 单位换算常量 英美 - 国际  厘米(cm) = 英寸(in) * 2.54
    private final static BigDecimal LENGTH_UNIT_CONVERT = BigDecimal.valueOf(2.54);
    // 单位换算常量 国际  磅(lb)  = 千克(KG) * 2.20462
    private final static BigDecimal WEIGHT_UNIT_CONVERT = BigDecimal.valueOf(2.20462);
    // 1 立方英寸 = 0.000016387064 立方米
    private final static BigDecimal VOLUME_UNIT_CONVERT = BigDecimal.valueOf(0.000016387064);
    // 1 立方米 = 61023.744094732 立方英寸
    private final static BigDecimal VOLUME_REVERSE_UNIT_CONVERT = BigDecimal.valueOf(61023.744094732);
    // 1 立方米 = 1000000 立方厘米

    // 国际制
    public final static Integer UNIT_INTERNATIONAL = 1;
    // 法英制
    public final static Integer UNIT_BRITAIN = 2;

    /**
     * 体积相关参数转换[长、宽、高] 法美英制(in) -> 国际制度(cm)
     * @param source
     * @return
     */
    public static BigDecimal volumeConvert(BigDecimal source,Integer sourceUnit,Integer targetUnit){
        if(null == source || null == targetUnit || null == sourceUnit){
            return source;
        }
        if(UNIT_BRITAIN.equals(sourceUnit) && UNIT_INTERNATIONAL.equals(targetUnit)){
            source = source.multiply(LENGTH_UNIT_CONVERT).setScale(2, RoundingMode.HALF_UP);
        }else if(UNIT_INTERNATIONAL.equals(sourceUnit) && UNIT_BRITAIN.equals(targetUnit)){
            source = source.divide(LENGTH_UNIT_CONVERT).setScale(2, RoundingMode.HALF_UP);
        }
        return source;
    }
    /**
     * 体积换算：立方英寸 -> 立方米
     * 1 立方英寸 = 0.000016387064 立方米
     * @param cubicInches 立方英寸
     * @return 立方米，保留两位小数，向上取整
     */
    public static BigDecimal cubicInchesToCubicMeters(BigDecimal cubicInches) {
        if (cubicInches == null) {
            return null;
        }
        return cubicInches.multiply(VOLUME_UNIT_CONVERT).setScale(2, RoundingMode.UP);
    }

    /**
     * 体积换算：立方米 -> 立方英寸
     * 1 立方米 = 61023.744094732 立方英寸
     * @param cubicMeters 立方米
     * @return 立方英寸，保留两位小数，向上取整
     */
    public static BigDecimal cubicMetersToCubicInches(BigDecimal cubicMeters) {
        if (cubicMeters == null) {
            return null;
        }
        return cubicMeters.multiply(VOLUME_REVERSE_UNIT_CONVERT).setScale(2, RoundingMode.UP);
    }

    /**
     * 重量相关参数转换[重量] 法美英制(lb) -> 国际制度 (kg)
     * @param source
     * @return
     */
    public static BigDecimal weightConvert(BigDecimal source,Integer sourceUnit,Integer targetUnit){
        if(null == source || null == targetUnit ){
            return source;
        }
        if(UNIT_BRITAIN.equals(sourceUnit) && UNIT_INTERNATIONAL.equals(targetUnit)){
            source = source.divide(WEIGHT_UNIT_CONVERT,2,RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP);
        }else if(UNIT_INTERNATIONAL.equals(sourceUnit) && UNIT_BRITAIN.equals(targetUnit)){
            source = source.multiply(WEIGHT_UNIT_CONVERT).setScale(2, RoundingMode.HALF_UP);
        }
        return source;
    }

    /**
     * 体积换算：立方厘米 -> 立方米
     *
     */
    public static BigDecimal volumeCM3ConvertToM3(BigDecimal volumeCM3){
        if (volumeCM3 == null) {
            return null;
        }
        return volumeCM3.divide(StoreConstants.VOLUME_CM3_CONVERT_M3,2,RoundingMode.UP);
    }

}

package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 区域信息表
 *
 * <AUTHOR>
 * @date 2025-03-10 17:25:38
 */
@Data
@TenantTable
@TableName("tms_region")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "区域信息表")
public class TmsRegionEntity extends BaseLogicEntity<TmsRegionEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long regionId;

	/**
	* 城市
	*/
    @Schema(description="城市")
    private String city;

	/**
	* 区域名称
	*/
    @Schema(description="区域名称")
    private String regionName;


	/**
	 * 承运商类型。1：自营、2：外包
	 */
	@Schema(description="承运商类型。1：自营、2：外包")
	private Integer carrierType;

	/**
	* 承运商
	*/
    @Schema(description="承运商")
    private Long carrierId;

	/**
	* 区域面积（km²）
	*/
    @Schema(description="区域面积（km²）")
    private BigDecimal area;

	/**
	* 划分的区域
	*/
    @Schema(description="划分的区域")
    private String areaGeometry;

	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;

}
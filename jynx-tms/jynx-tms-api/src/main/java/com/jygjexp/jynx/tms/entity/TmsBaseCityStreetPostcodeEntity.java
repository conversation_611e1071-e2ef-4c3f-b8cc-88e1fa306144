package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 加拿大邮编地址库
 *
 * <AUTHOR>
 * @date 2025-09-02 16:17:40
 */
@Data
@TableName("tms_base_city_street_postcode")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "加拿大邮编地址库")
public class TmsBaseCityStreetPostcodeEntity extends Model<TmsBaseCityStreetPostcodeEntity> {


	/**
	* ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="ID")
    private Integer id;

	/**
	* 三字邮编
	*/
    @Schema(description="三字邮编")
    private String threePostCode;

	/**
	* 六字邮编
	*/
    @Schema(description="六字邮编")
    private String sixPostCode;

	/**
	* 城市
	*/
    @Schema(description="城市")
    private String city;

	/**
	* 省州
	*/
    @Schema(description="省州")
    private String province;

	/**
	* 街道
	*/
    @Schema(description="街道")
    private String rstreet;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String memo;

	/**
	* 0/1 1：有效 0无效
	*/
    @Schema(description="0/1")
    private Integer status;

	/**
	 * 地址类型
	 */
	@Schema(description="地址类型")
	private Integer addressType;


	/**
	 * 版本
	 */
	@Schema(description="版本")
	private Integer version;


	/**
	 * 是否异常地址
	 */
	@Schema(description="是否异常地址")
	private Integer isAbnormal;


	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createDate;


	/**
	* 更新时间
	*/
    @Schema(description="更新时间")
    private LocalDateTime updateDate;

}
package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 笼车信息
 *
 * <AUTHOR>
 * @date 2025-04-18 14:10:07
 */
@Data
@TenantTable
@TableName("tms_cage")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "容器信息")
public class TmsCageEntity extends BaseLogicEntity<TmsCageEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	 * 容器id
	 */
	@Schema(description="容器id")
	private Long cageId;

	/**
	 * 仓库类型
	 */
	@Schema(description="仓库类型")
	private Integer siteType;

	/**
	* 容器编码
	*/
    @Schema(description="容器编码")
    private String cageCode;

	/**
	* 容器名称
	*/
    @Schema(description="容器名称")
    private String cageName;

	/**
	 * 容器类型
	 */
	@Schema(description="容器类型")
	private Integer cageType;


	/**
	* 仓库id
	*/
    @Schema(description="仓库id")
    private Long siteId;

	/**
	* 容器容量m³
	*/
    @Schema(description="容器容量m³")
    private BigDecimal capacity;

	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;

}
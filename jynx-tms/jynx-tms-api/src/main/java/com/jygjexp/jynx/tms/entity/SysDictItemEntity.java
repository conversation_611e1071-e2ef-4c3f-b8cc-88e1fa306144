package com.jygjexp.jynx.tms.entity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SysDictItemEntity {
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "字典ID")
    private Long dictId;

    @Schema(description = "标签名称")
    private String label;

    @Schema(description = "字典类型")
    private String dictType;

    @Schema(description = "描述信息")
    private String description;

    @Schema(description = "排序号")
    private Integer sortOrder;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "更新者")
    private String updateBy;

    @Schema(description = "创建时间")
    private String createTime;

    @Schema(description = "更新时间")
    private String updateTime;

    @Schema(description = "备注")
    private String remarks;

    @Schema(description = "删除标识（0:未删除, 1:已删除）")
    private String delFlag;

    @Schema(description = "字典值")
    private String value;

}

package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 照片库
 *
 * <AUTHOR>
 * @date 2024-09-30 18:22:56
 */
@Data
@TableName("tkzj_zt_photo")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "照片库")
public class TmsPhotoEntity extends Model<TmsPhotoEntity> {


	/**
	* 照片ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="照片ID")
    private Integer photoId;

	/**
	* 所属驿站
	*/
    @Schema(description="所属驿站")
    private Integer ownerType;

	/**
	* 子类型
	*/
    @Schema(description="子类型")
    private Integer subType;

	/**
	* 驿站ID
	*/
    @Schema(description="驿站ID")
    private Integer postId;

	/**
	* 添加时间
	*/
    @Schema(description="添加时间")
    private LocalDateTime createDate;

	/**
	* 路径
	*/
    @Schema(description="路径")
    private String filePath;

	/**
	* 大小
	*/
    @Schema(description="大小")
    private Long fileSize;

	/**
	* 宽
	*/
    @Schema(description="宽")
    private Integer width;

	/**
	* 高
	*/
    @Schema(description="高")
    private Integer height;

	/**
	* 排序（小靠前）
	*/
    @Schema(description="排序（小靠前）")
    private Integer priority;
}
package com.jygjexp.jynx.tms.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: chenchang
 * @Description: 客户订单状态枚举
 * @Date: 2025/3/13 15:18
 */
public enum CustomerOrderStatus {
    /**
     * 待指派
     */
    UNASSIGNED(23001, "UNASSIGNED"),
    /**
     * 已取消-客户端、管理后台用
     */
    CANCELLED(23002, "CANCELLED"),
    /**
     * 已指派
     */
    ASSIGNED(23003, "ASSIGNED"),
    /**
     * 已驳回
     */
    REJECTED(23004, "REJECTED"),
    /**
     * 待运输-客户端用
     */
    PENDING_SHIPMENT(23005, "PENDING_SHIPMENT"),
    /**
     * 待收货
     */
    PENDING_RECEIPT(23006, "PENDING_RECEIPT"),
    /**
     * 已完成
     */
    DELIVERED(23007, "DELIVERED"),

    /**
     * 已拦截
     */
    BLOCK(23008, "BLOCK");

    CustomerOrderStatus(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    // 状态码
    private final Integer code;

    // 状态值
    private final String value;

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    // 用于快速查找的 Map
    private static final Map<Integer, CustomerOrderStatus> LOOKUP = new HashMap<>();

    // 静态初始化，将所有枚举项放入 Map 中
    static {
        for (CustomerOrderStatus status : EnumSet.allOf(CustomerOrderStatus.class)) {
            LOOKUP.put(status.code, status);
        }
    }

    /**
     * 根据 code 获取枚举项
     *
     * @param code 状态码
     * @return 对应的枚举项，如果不存在则返回 null
     */
    public static CustomerOrderStatus lookup(Integer code) {
        return LOOKUP.get(code);
    }
}

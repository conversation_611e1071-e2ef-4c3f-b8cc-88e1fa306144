package com.jygjexp.jynx.tms.enums;

import lombok.Getter;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
public enum BusinessType {
    COLLECTION(1, "COLLECTION"),
    DELIVERY(2, "DELIVERY"),
    HALF_TRUSTEESHIP(5,"HALF_TRUSTEESHIP");

    private final Integer code;
    private final String value;

    BusinessType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    // 枚举缓存 map
    private static final Map<Integer, BusinessType> LOOKUP = new HashMap<>();

    static {
        for (BusinessType type : BusinessType.values()) {
            LOOKUP.put(type.getCode(), type);
        }
    }

    /**
     * 根据 code 获取枚举项
     */
    public static BusinessType lookup(Integer code) {
        return LOOKUP.get(code);
    }
}

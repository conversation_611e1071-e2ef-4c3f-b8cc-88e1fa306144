package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 标签信息
 *
 * <AUTHOR>
 * @date 2025-04-20 20:10:07
 */
@Data
@TenantTable
@TableName("tms_label")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "标签信息")
public class TmsLabelEntity extends BaseLogicEntity<TmsLabelEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

	/**
	 * 标签编码
	 */
	@Schema(description="标签编码")
	private String labelCode;

	/**
	* 容器id
	*/
    @Schema(description="容器id")
    private Long cageId;

	/**
	* 容器名称
	*/
    @Schema(description="容器名称")
    private String cageName;

	/**
	* 容器类型 0：铁笼 1：箱 2：托盘
	*/
    @Schema(description="容器类型 0：铁笼 1：箱 2：托盘")
    private Integer cageType;

	/**
	* 仓库id
	*/
    @Schema(description="仓库id")
    private Long siteId;

	/**
	* 始发仓库
	*/
    @Schema(description="始发仓库")
    private Long startSiteId;

	/**
	* 目的仓库
	*/
    @Schema(description="目的仓库")
    private Long endSiteId;

	/**
	* 订单数量
	*/
    @Schema(description="订单数量")
    private Integer orderNumber;

	/**
	* 标签使用状态（0未使用，已使用）
	*/
    @Schema(description="标签使用状态（0未使用，1已使用）")
    private Integer labelStatus;

	/**
	 * 干线是否扫码提货（0：未扫码提货，1：已扫描提货）
	 */
	@Schema(description = "干线是否扫码提货（0：未扫码提货，1：已扫描提货）")
	private Integer isPickupProof;

}
package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDate;

/**
 * 中大件派送订单统计表
 *
 * <AUTHOR>
 * @date 2025-06-25 19:01:48
 */
@Data
@TableName("tms_order_statistics")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "中大件派送订单统计表")
public class TmsOrderStatisticsEntity extends Model<TmsOrderStatisticsEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 日期
	*/
    @Schema(description="日期")
    private LocalDate date;

	/**
	* 揽收量
	*/
    @Schema(description="揽收量")
    private Integer pickupNum;

	/**
	* 派送量
	*/
    @Schema(description="派送量")
    private Integer dispatchNum;

	/**
	* yyz揽收量
	*/
    @Schema(description="yyz揽收量")
    private Integer yyzPickupNum;

	/**
	* yyz派送
	*/
    @Schema(description="yyz派送")
    private Integer yyzDispatchNum;

	/**
	* yvr揽收量
	*/
    @Schema(description="yvr揽收量")
    private Integer yvrPickupNum;

	/**
	* yvr派送量
	*/
    @Schema(description="yvr派送量")
    private Integer yvrDispatchNum;
}
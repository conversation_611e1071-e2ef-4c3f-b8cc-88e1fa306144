package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 中大件人工分拣记录
 *
 * <AUTHOR>
 * @date 2025-04-23 17:59:39
 */
@Data
@TenantTable
@TableName("tms_manual_sorting_record")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "中大件人工分拣记录")
public class TmsManualSortingRecordEntity extends BaseLogicEntity<TmsManualSortingRecordEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	 * 当前分拣仓库id
	 */
	@Schema(description="当前分拣仓库id")
	private Long sortingWarehouseId;


	/**
	* 匹配仓库id
	*/
    @Schema(description="匹配仓库id")
    private Long warehouseId;

	/**
	* 分拣格口代码
	*/
    @Schema(description="分拣格口代码")
    private String sortingGridCode;


	@Schema(description="客户单号/客户参考单号")
	private String customerOrderNumber;


	/**
	* 跟踪单号
	*/
    @Schema(description="跟踪单号")
    private String entrustedOrderNo;


	//在数据库排除该字段
	@TableField(exist = false)
	@Schema(description = "是否使用客户单号作为面单号")
	private Boolean isCustomerLabel;

	@Schema(description = "长(cm)")
	private BigDecimal length;

	@Schema(description = "宽(cm)")
	private BigDecimal width;

	@Schema(description = "高(cm)")
	private BigDecimal height;

	@Schema(description = "体积(m³)")
	private BigDecimal volume;


	@Schema(description = "重量(kg)")
	private BigDecimal weight;

	/**
	* 分拣时间
	*/
    @Schema(description="分拣时间")
    private LocalDateTime sortingTime;

	/**
	 * 分拣次数
	 */
	@Schema(description="分拣次数")
	private Integer sortingFrequency;

	/**
	 * 路线编号
	 */
	@Schema(description="路线编号")
	private String routeNumber;

}
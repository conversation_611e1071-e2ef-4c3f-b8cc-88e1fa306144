package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jygjexp.jynx.common.core.util.TenantTable;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 卡派地址簿
 *
 * <AUTHOR>
 * @date 2025-03-03 20:42:43
 */
@Data
@TenantTable
@TableName("tms_addressbook")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卡派地址簿")
public class TmsAddressbookEntity extends BaseLogicEntity<TmsAddressbookEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	 * 客户id
	 */
	@Schema(description="客户id")
	private Long customerId;


	@Schema(description = "客户名称")
	private String customerName;

	/**
	* 联系人
	*/
    @Schema(description="联系人")
    private String contacts;

	/**
	* 联系电话
	*/
    @Schema(description="联系电话")
    private String contactPhone;

	/**
	* 国家/地区
	*/
    @Schema(description="国家/地区")
    private String countryName;

	/**
	* 省/州
	*/
    @Schema(description="省/州")
    private String statesName;

	/**
	* 城市
	*/
    @Schema(description="城市")
    private String cityName;

	/**
	* 邮政编码
	*/
    @Schema(description="邮政编码")
    private String postalCode;

	/**
	* 地址类型
	*/
    @Schema(description="地址类型")
    private String addressType;

	/**
	* 详细地址
	*/
    @Schema(description="详细地址")
    private String detailedAddress;

	/**
	 * 详细地址2
	 */
	@Schema(description="详细地址2")
	private String detailedAddress2;

	/**
	* 启用状态：0 禁用，1 启用
	*/
    @Schema(description="启用状态：0 禁用，1 启用")
    private Integer isValid;



}
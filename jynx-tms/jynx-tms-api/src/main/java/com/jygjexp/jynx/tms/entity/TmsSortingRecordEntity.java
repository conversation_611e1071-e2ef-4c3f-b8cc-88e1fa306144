package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 分拣记录表
 *
 * <AUTHOR>
 * @date 2025-06-25 11:36:17
 */
@Data
@TenantTable
@TableName("tms_sorting_record")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "分拣记录表")
public class TmsSortingRecordEntity extends Model<TmsSortingRecordEntity> {


	/**
	* id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private Long id;

	/**
	* 模版id
	*/
    @Schema(description="模版id")
    private Long templateId;

	/**
	* 模版名称
	*/
    @Schema(description="模版名称")
    private String templateName;

	/**
	* 格口id
	*/
    @Schema(description="格口id")
    private Integer grid;

	/**
	* 分拣单号
	*/
    @Schema(description="分拣单号")
    private String orderNo;

	/**
	 * 面单号
	 */
	@Schema(description = "面单号")
	private String billOrderNo;

	/**
	 * 系统主单号
	 */
	@Schema(description = "主单号:0否;1是")
	private Integer mainFlag;

	/**
	* 分拣扫描时间
	*/
    @Schema(description="分拣扫描时间")
    private LocalDateTime scanTime;

	/**
	* 分拣状态
	*/
    @Schema(description="分拣状态")
    private Integer sortingStatus;

	/**
	* 路线号
	*/
    @Schema(description="路线号")
    private String routeNumber;

	/**
	* 城市
	*/
    @Schema(description="城市")
    private String city;

	/**
	* 收货地
	*/
    @Schema(description="收货地")
    private String receivePlace;

	/**
	* 重量（kg）
	*/
    @Schema(description="重量（kg）")
    private BigDecimal weight;

	/**
	* 体积（m³）
	*/
    @Schema(description="体积（m³）")
    private BigDecimal volume;

	/**
	* 机器编号
	*/
    @Schema(description="机器编号")
    private String machineNumber;

	/**
	* 商家对应id（字典值）
	*/
    @Schema(description="商家对应id（字典值）")
    private Integer merchant;

	/**
	* 分拣类型（1：揽收退件、2：正向派送）
	*/
    @Schema(description="分拣类型（1：揽收退件;2：正向派送;5:半托管）")
    private Integer type;

	/**
	* 状态
	*/
    @Schema(description="状态")
    private Integer status;

	/**
	 * 失败原因
	 */
	@Schema(description="失败原因")
	private String failureReason;

	/**
	 * 拓展数据
	 */
	@Schema(description = "拓展数据")
	private String extend;

	/**
	 * 包裹重量（kg）
	 */
	@Schema(description = "包裹重量（kg）", type = "string")
	private BigDecimal packageWeight;


	/**
	 * 包裹体积（m³）
	 */
	@Schema(description = "包裹体积（m³）", type = "string")
	private BigDecimal packageVolume;

	/**
	 * 长（CM）
	 */
	@Schema(description = "长（CM）")
	private BigDecimal packageLength;

	/**
	 * 宽（CM）
	 */
	@Schema(description = "宽（CM）")
	private BigDecimal packageWidth;

	/**
	 * 高（CM）
	 */
	@Schema(description = "高（CM）")
	private BigDecimal packageHeight;

	/**
	* 站点id
	*/
    @Schema(description="站点id")
    private Integer siteId;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Integer revision;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 逻辑删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="逻辑删除")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Integer tenantId;
}

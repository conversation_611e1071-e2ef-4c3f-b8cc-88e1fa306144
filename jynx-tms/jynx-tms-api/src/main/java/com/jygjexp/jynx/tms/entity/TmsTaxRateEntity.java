package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 税率维护
 *
 * <AUTHOR>
 * @date 2025-07-11 10:33:37
 */
@Data
@TenantTable
@TableName("tms_tax_rate")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "税率维护")
public class TmsTaxRateEntity extends BaseLogicEntity<TmsTaxRateEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 省份
	*/
    @Schema(description="省份")
    private String province;

	/**
	* GST(%)
	*/
    @Schema(description="GST(%)")
    private BigDecimal gst;

	/**
	* PST(%)
	*/
    @Schema(description="PST(%)")
    private BigDecimal pst;

	/**
	* HST(%)
	*/
    @Schema(description="HST(%)")
    private BigDecimal hst;

	/**
	* QST(%)
	*/
    @Schema(description="QST(%)")
    private BigDecimal qst;

	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;
}
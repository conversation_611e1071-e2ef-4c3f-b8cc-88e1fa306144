package com.jygjexp.jynx.tms.enums;

import com.fasterxml.jackson.annotation.JsonValue;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: chenchang
 * @Description: 附加服务类型
 * @Date: 2025/3/7 11:13
 */
public enum AdditionalServiceType {
    INSURANCE(1, "保险"),

    APPOINTMENT_FOR_PICKUP(2, "预约提货"),

    APPOINTMENT_FOR_DELIVERY(3, "预约送货"),

    WEEKEND_PICKUP(4, "周末提货"),

    WEEKEND_DELIVERY(5, "周末送货"),

    DELIVERY_OUTSIDE_OF_WORKING_HOURS(6, "工作时间外送货"),

    PICK_UP_OUTSIDE_OF_WORKING_HOURS(7, "工作时间外取货"),

    TAIL_BOARD_PICKUP(8, "尾板提货"),

    TAIL_BOARD_UNLOADING(9, "尾板卸货");

    // 状态码
    private final Integer code;
    // 状态值
    private final String value;

    AdditionalServiceType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }
    public String getValue() {
        return value;
    }

    private static final Map<Integer, AdditionalServiceType> LOOKUP = new HashMap<>();

    // 静态初始化，将所有枚举项放入 Map 中
    static {
        for (AdditionalServiceType status : EnumSet.allOf(AdditionalServiceType.class)) {
            LOOKUP.put(status.code, status);
        }
    }

    /**
     * 根据 code 获取枚举项
     *
     * @param code 状态码
     * @return 对应的枚举项，如果不存在则返回 null
     */
    public static AdditionalServiceType lookup(Integer code) {
        return LOOKUP.get(code);
    }

}

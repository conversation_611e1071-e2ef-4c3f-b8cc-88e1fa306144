package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;

import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * 门店员工表
 *
 * <AUTHOR>
 * @date 2025-07-11 14:00:19
 */
@Data
@TenantTable
@TableName("tms_store_employee")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门店员工表")
public class TmsStoreEmployeeEntity extends Model<TmsStoreEmployeeEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键ID")
    private Long id;

	/**
	 * 用户ID
	 */
	@Schema(description = "用户ID")
	private Long userId;

	/**
	* 员工名称
	*/
	@Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9_]*$", message = "用户名必须以字母开头，只包含字母、数字和下划线")
    @Schema(description="员工名称")
    private String employeeName;

	/**
	* 手机号
	*/
    @Schema(description="手机号")
    private String employeePhone;

	/**
	* 员工类型
	*/
    @Schema(description="员工类型")
    private String employeeType;

	/**
	* 启用状态 0、未启用；1、启用
	*/
    @Schema(description="启用状态 0、未启用；1、启用")
    private Integer status;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Long revision;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标志
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Long tenantId;
}
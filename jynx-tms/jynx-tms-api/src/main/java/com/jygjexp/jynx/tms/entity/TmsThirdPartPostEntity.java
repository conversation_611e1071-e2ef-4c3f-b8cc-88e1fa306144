package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 换单列表
 *
 * <AUTHOR>
 * @date 2025-06-12 18:06:06
 */
@Data
@TableName("tms_third_part_post")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "换单列表")
public class TmsThirdPartPostEntity extends Model<TmsThirdPartPostEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

	/**
	* 渠道
	*/
    @Schema(description="渠道")
    private String channel;

	/**
	* 面单
	*/
    @Schema(description="面单")
    private String labelUrl;

	/**
	* NB系统单号
	*/
    @Schema(description="NB系统单号")
    private String nbOrderNo;

	/**
	* 渠道单号
	*/
    @Schema(description="渠道单号")
    private String channelOrderNo;

	/**
	* 客户单号
	*/
    @Schema(description="客户单号")
    private String customerOrderNo;

	/**
	* 时间
	*/
    @Schema(description="时间")
    private LocalDateTime time;

	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description = "创建人")
	private String createBy;
}
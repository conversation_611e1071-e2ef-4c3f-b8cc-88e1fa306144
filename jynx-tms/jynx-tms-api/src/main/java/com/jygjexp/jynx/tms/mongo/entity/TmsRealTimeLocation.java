package com.jygjexp.jynx.tms.mongo.entity;

import com.mongoplus.annotation.ID;
import com.mongoplus.annotation.collection.CollectionName;
import com.mongoplus.enums.IdTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "实时经纬度")
@CollectionName("real_time_location")
public class TmsRealTimeLocation {
    /**
     * id
     */
    @ID(type = IdTypeEnum.ASSIGN_ID)
    @Schema(description = "id")
    private Long id;

    /**
     * 路线id
     */
    @Schema(description ="路线id")
    private Long vehicleRouteId;
    /**
     * 维度
     */
    @Schema(description = "维度")
    private BigDecimal lat;
    /**
     * 经度
     */
    @Schema(description = "经度")
    private BigDecimal lng;
    /**
     * 上传时间
     */
    @Schema(description = "上传时间")
    private LocalDateTime uploadTime;
}

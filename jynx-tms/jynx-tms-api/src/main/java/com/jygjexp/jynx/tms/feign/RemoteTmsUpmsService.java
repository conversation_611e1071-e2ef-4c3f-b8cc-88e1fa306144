package com.jygjexp.jynx.tms.feign;


import com.jygjexp.jynx.admin.api.dto.*;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.admin.api.vo.UserVO;
import com.jygjexp.jynx.common.core.constant.SecurityConstants;
import com.jygjexp.jynx.common.core.constant.ServiceNameConstants;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@FeignClient(contextId = "remoteTmsUpmsService", value = ServiceNameConstants.UPMS_SERVICE)
public interface RemoteTmsUpmsService {

    /**
     * 新增用户信息
     *
     * @return 用户信息
     */
    @PostMapping("/user")
    R addUser(@RequestBody UserDTO userDto);

    // 修改用户信息
    @PutMapping("/user")
     R updateUser(@Valid @RequestBody UserDTO userDto);

    /**
     * 删除用户信息
     * @param ids ID
     * @return R
     */
    @DeleteMapping ("/user")
    R userDel(@RequestBody Long[] ids) ;

    /**
     * 通过用户名查询用户、角色信息
     * @param username 用户名
     * @return R
     */
    @GetMapping("/user/info/{username}")
    @NoToken
    R<UserInfo> info(@PathVariable("username") String username);

    /**
     * 根据手机号查询用户、角色信息
     * @return 用户信息
     */
    @NoToken
    @GetMapping("/user/getUserInfoByPhone/{phone}")
    R<UserInfo>  getUserInfoByPhone(@PathVariable String phone);

    /**
     * 新增客户\承运商账号
     */
    @PostMapping("/user/add")
    R<Boolean> addAccount(@RequestParam String username,@RequestParam("password") String password ,@RequestParam(value = "email",required = false) String email,@RequestParam("phone") String phone,@RequestParam("isTag") Boolean isTag,@RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 锁定用户
     * @param username 用户名
     * @param from 调用标识
     * @return
     */
    @PutMapping("/user/lock/{username}")
    R<Boolean> lockUser(@PathVariable("username") String username, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 启用账号
     * @param username 用户名
     * @return
     */
    @PutMapping("/user/enable/{username}")
    R<Boolean> enableById(@PathVariable("username") String username, @RequestHeader(SecurityConstants.FROM) String from);



    /**
     * 修改客户账号信息
     * @param userDto userDto
     * @return success/false
     */
    @PutMapping("/user/customer/edit")
    R updateCustomerInfo(@Valid @RequestBody UserDTO userDto);

    /**
     * 根据手机号查询客户账号信息
     * @param phone
     * @return
     */
    @GetMapping("/user/getCustomerUserByPhone")
    R<SysUser> getCustomerUserByPhone(@RequestParam String phone);

    /**
     * 根据用户ID查询客户账号信息
     * @param userId
     * @return
     */
    @GetMapping("/user/getCustomerUserByUserId")
    R <SysUser>getCustomerUserByUserId(@RequestParam Long userId);


    @PutMapping("/user/updateEmail")
    R updateEmail(@RequestBody ResetEmailDTO resetEmailDTO);

    @PutMapping("/user/updatePhone")
    R updatePhone(@RequestBody ResetPhoneDTO resetPhoneDTO);

    @PutMapping("/user/resetLoginPwd")
    R resetLoginPwd(@RequestBody ResetPwdDTO resetPwdDTO);

    /**
     * 根据邮箱查询客户账号信息
     * @param email
     * @return
     */
    @GetMapping("/user/getCustomerUserByEmail")
    R <SysUser>getCustomerUserByEmail(@RequestParam String email);

    /**
     * 根据手机号和用户名查询账号信息
     */
    @GetMapping("/user/getCustomerUserByPhoneOrUsername")
    R<Boolean> getCustomerUserByPhoneOrUsername(@RequestParam String phone, @RequestParam String username);

    /**
     * 根据手机号和用户名查询指定账号信息
     */
    @GetMapping("/user/getCustomerUserIdByPhoneOrUsername/{userId}")
    R<Boolean> getCustomerUserIdByPhoneOrUsername(@RequestParam(required = false) String phone, @RequestParam(required = false) String username,  @PathVariable("userId") Long userId);

    /**
     * 获取用户详情
     */
    @GetMapping("/user/details/{id}")
    R<UserVO> userDetail(@PathVariable Long id);

    /**
     * 锁定用户信息
     */
    @PutMapping("/user/locks")
    R<Boolean> userLocks(@RequestBody List<Long> ids);

    /**
     * 启用用户信息
     */
    @PutMapping("/user/enables")
    R<Boolean> userEnables(@RequestBody List<Long> ids);

    /**
     * 新增用户信息
     *
     * @return 用户信息
     */
    @PostMapping("/user/addNoToken")
    R addUserNoToken(@RequestBody UserDTO userDto);

    /**
     * 删除用户信息
     * @param ids ID
     * @return R
     */
    @DeleteMapping("/user/delNoToken")
    R userDelNoToken(@RequestBody Long[] ids);
}

package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 轨迹webhook
 *
 * <AUTHOR>
 * @date 2025-08-18 13:56:15
 */
@Data
@TableName("tms_track_webhook")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "轨迹webhook")
public class TmsTrackWebhookEntity extends Model<TmsTrackWebhookEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 单号
	*/
    @Schema(description="单号")
    private String orderNo;

	/**
	* 客户id
	*/
    @Schema(description="客户id")
    private Long customerId;

	/**
	* 回调URL
	*/
    @Schema(description="回调URL")
    private String webhookUrl;

	/**
	* 0=失败，1=成功
	*/
    @Schema(description="0=失败，1=成功")
    private Integer status;

	/**
	* 回调时间
	*/
    @Schema(description="回调时间")
    private LocalDateTime callbackTime;

	/**
	* 轨迹id
	*/
    @Schema(description="轨迹id")
    private String trackId;

	/**
	* 失败提示
	*/
    @Schema(description="失败提示")
    private String errorMsg;

	/**
	 * 是否推送 0=未推送，1=已推送
	 */
	@Schema(description="是否推送 0=未推送，1=已推送")
	private Integer isPush;

	/**
	 * 推送状态：成功，失败
	 */
	@Schema(description="推送状态：成功，失败")
	private String pushStatus;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;
}
package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.github.yulichang.annotation.EntityMapping;
import java.util.List;

/**
 * 服务商利润配置
 *
 * <AUTHOR>
 * @date 2025-08-26 10:46:10
 */
@Data
@TenantTable
@TableName("tms_store_profit_config")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "服务商利润配置")
public class TmsStoreProfitConfigEntity extends Model<TmsStoreProfitConfigEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

	/**
	* 服务商id
	*/
    @Schema(description="服务商id")
    private Long providerId;

	/**
	* 生效开始时间
	*/
    @Schema(description="生效开始时间")
    private LocalDateTime validStartTime;

	/**
	* 生效结束时间
	*/
    @Schema(description="生效结束时间")
    private LocalDateTime validEndTime;

	/**
	* 规则类型
	*/
    @Schema(description="规则类型 0-百分比 1-固定值 2-绝对价格")
    private Integer ruleType;

	/**
	* 单位
	*/
    @Schema(description="单位 1-KG 2-LB")
    private Integer unit;

	/**
	* 是否应用盲盒 1是 0否
	*/
    @Schema(description="是否应用盲盒 1是 0否")
    private Integer blindBoxFlag;

	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Long revision;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标志
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Long tenantId;

    @ExcelIgnore
    @TableField(exist = false)
    @EntityMapping(thisField = "id", joinField = "profitId")
	@Schema(description="服务商利润配置重量段明细")
    private List<TmsStoreProfitConfigDetailEntity> tmsStoreProfitConfigDetailList;
}
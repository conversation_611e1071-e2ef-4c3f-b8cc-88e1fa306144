package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 卡派-运输单
 *
 * <AUTHOR>
 * @date 2025-03-13 10:56:31
 */
@Data
@TenantTable
@TableName("tms_shipment_order")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卡派-运输单")
public class TmsShipmentOrderEntity extends BaseLogicEntity<TmsShipmentOrderEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long shipmentId;

	/**
	* 运输单号
	*/
    @Schema(description="运输单号")
    private String shipmentNo;

	/**
	* 状态（0-待提货，1-运输中，2-已完成，3-异常）
	*/
    @Schema(description="状态（0-待提货，1-运输中，2-已完成，3-已失败）")
    private Integer shipmentStatus;

	/**
	* 司机ID，关联司机表
	*/
    @Schema(description="司机ID，关联司机表")
    private Long driverId;

	/**
	* 车牌号
	*/
    @Schema(description="车牌号")
    private String licensePlate;

	/**
	* 司机联系方式
	*/
    @Schema(description="司机联系方式")
    private String contactPhone;

	/**
	* 车辆类型
	*/
    @Schema(description="车辆类型")
    private Integer vehicleType;


	/**
	* 总体积(m³)
	*/
    @Schema(description="总体积(m³)")
    private BigDecimal totalVolume;

	/**
	* 总重量(kg)
	*/
    @Schema(description="总重量(kg)")
    private BigDecimal totalWeight;

	/**
	* 总件数
	*/
    @Schema(description="总件数")
    private Integer totalQuantity;

}
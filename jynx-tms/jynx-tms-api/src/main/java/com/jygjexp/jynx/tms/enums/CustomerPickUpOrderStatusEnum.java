package com.jygjexp.jynx.tms.enums;

import lombok.Getter;

@Getter
public enum CustomerPickUpOrderStatusEnum {


    TBPU(0, "待取货"),
    RW(1, "取货中"),
    CANCEL(2, "已取消"),
    COMPLETE(3, "已完成")
            ;

    private final Integer code;
    private final String info;


    CustomerPickUpOrderStatusEnum(Integer code, String info) {
        this.code = code;
        this.info = info;
    }
    public static CustomerPickUpOrderStatusEnum getInstance(Integer code) {
        if (code == null) {
            return null;
        }
        for (CustomerPickUpOrderStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}

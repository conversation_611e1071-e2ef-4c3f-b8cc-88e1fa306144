package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.core.util.TenantTable;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 卡派 签收照片
 *
 * <AUTHOR>
 * @date 2025-02-12 18:23:01
 */
@Data
@TenantTable
@TableName("tms_order_sign_image")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卡派 签收照片")
public class TmsOrderSignImageEntity extends BaseLogicEntity<TmsOrderSignImageEntity> {


    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

    /**
     * 订单ID
     */
    @Schema(description="订单ID")
    private Integer orderId;

    /**
     * 订单编号
     */
    @Schema(description="订单编号")
    private String orderNo;

    /**
     * 包裹编号
     */
    @Schema(description="包裹编号")
    private String pkgNo;

    /**
     * 包裹图
     */
    @Schema(description="包裹图")
    private String pkgImage;

    /**
     * 放置图
     */
    @Schema(description="放置图")
    private String putImage;

    /**
     * 订单状态
     */
    @Schema(description="订单状态")
    private Integer orderStatus;

    /**
     * 司机名称
     */
    @Schema(description="司机名称")
    private String driverName;

    /**
     * 司机ID
     */
    @Schema(description="司机ID")
    private Long driverId;

    /**
     * 员工ID
     */
    @Schema(description="员工ID")
    private Long staffId;

    /**
     * 纬度
     */
    @Schema(description="纬度")
    private BigDecimal lat;

    /**
     * 经度
     */
    @Schema(description="经度")
    private BigDecimal lng;

    /**
     * 离收件距离
     */
    @Schema(description="离收件距离")
    private BigDecimal distance;


}
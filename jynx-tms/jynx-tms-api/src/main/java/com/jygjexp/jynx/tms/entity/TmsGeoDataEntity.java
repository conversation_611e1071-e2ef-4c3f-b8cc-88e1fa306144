package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 加拿大边界数据
 *
 * <AUTHOR>
 * @date 2025-03-23 18:39:11
 */
@Data
@TableName("tms_geo_data")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "加拿大边界数据")
public class TmsGeoDataEntity extends Model<TmsGeoDataEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 三字邮编key
	*/
    @Schema(description="三字邮编key")
    private String postKey;

	/**
	* 所代表位置
	*/
    @Schema(description="所代表位置")
    private String province;

	/**
	* 点位数据
	*/
    @Schema(description="点位数据")
    private String geometry;

	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description = "创建人")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description = "创建时间")
	private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.UPDATE)
	@Schema(description = "更新人")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.UPDATE)
	@Schema(description = "更新时间")
	private LocalDateTime updateTime;

}
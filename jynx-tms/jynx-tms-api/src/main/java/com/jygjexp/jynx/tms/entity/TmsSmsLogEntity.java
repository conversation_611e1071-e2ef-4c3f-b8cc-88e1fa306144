package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 中大件短信发送日志表
 *
 * <AUTHOR>
 * @date 2025-04-23 18:23:51
 */
@Data
@TableName("tms_sms_log")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "中大件短信发送日志表")
public class TmsSmsLogEntity extends Model<TmsSmsLogEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 手机号
	*/
    @Schema(description="手机号")
    private String mobile;

	/**
	* 模板内容（JSON字符串）
	*/
    @Schema(description="模板内容（JSON字符串）")
    private String templateParam;

	/**
	* 是否国内短信：1=是，0=否
	*/
    @Schema(description="是否国内短信：1=是，0=否")
    private Integer isDomestic;

	/**
	* 发送状态：0=失败，1=成功
	*/
    @Schema(description="发送状态：0=失败，1=成功")
    private Integer sendStatus;

	/**
	* 短信平台响应码
	*/
    @Schema(description="短信平台响应码")
    private String responseCode;

	/**
	* 短信平台响应消息
	*/
    @Schema(description="短信平台响应消息")
    private String responseMsg;

	/**
	* 错误信息或异常堆栈（如有）
	*/
    @Schema(description="错误信息或异常堆栈（如有）")
    private String errorMsg;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createdTime;

	/**
	* 更新时间
	*/
    @Schema(description="更新时间")
    private LocalDateTime updatedTime;
}
package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单子表
 *
 * <AUTHOR>
 * @date 2025-03-18 20:58:49
 */
@Data
@TableName("tkzj_zt_sub_order")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "子订单表")
public class SubOrderEntity extends Model<SubOrderEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Integer id;

	/**
	* 主单号
	*/
    @Schema(description="主单号")
    private String orderNo;

	/**
	* 子单号
	*/
    @Schema(description="子单号")
    private String subNo;

	/**
	* 订单状态
	*/
    @Schema(description="订单状态")
    private Integer status;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createDate;

	/**
	* 入站时间
	*/
    @Schema(description="入站时间")
    private LocalDateTime postInTime;

	/**
	* 驿站员工ID
	*/
    @Schema(description="驿站员工ID")
    private Integer postEmployeeId;

	/**
	* 司机ID
	*/
    @Schema(description="司机ID")
    private Integer driverId;

	/**
	* 司机取件时间
	*/
    @Schema(description="司机取件时间")
    private LocalDateTime driverTime;

	/**
	* 仓库ID
	*/
    @Schema(description="仓库ID")
    private Integer warehouseId;

	/**
	* 入库时间
	*/
    @Schema(description="入库时间")
    private LocalDateTime warehouseTime;

	/**
	* 退回客户时间
	*/
    @Schema(description="退回客户时间")
    private LocalDateTime returnCustomerTime;

	/**
	* 箱号
	*/
    @Schema(description="箱号")
    private String bagNum;

	/**
	* 长度
	*/
    @Schema(description="长度")
	private BigDecimal length;

	/**
	* 宽度
	*/
    @Schema(description="宽度")
	private BigDecimal width;

	/**
	* 高度
	*/
    @Schema(description="高度")
	private BigDecimal height;

	/**
	* 重量
	*/
    @Schema(description="重量")
	private BigDecimal weight;

	/**
	 * 单箱货物最大重量
	 */
	private BigDecimal bigWeight;


	/**
	 * 货物描述
	 */
	private String cargoDescription;

	/**
	 * 单箱货物数量
	 */
	private Integer quantity;


}
package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 服务商利润配置
 *
 * <AUTHOR>
 * @date 2025-08-26 10:46:10
 */
@Data
@TableName("tms_store_profit_config_detail")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "服务商利润配置重量段明细")
public class TmsStoreProfitConfigDetailEntity extends Model<TmsStoreProfitConfigDetailEntity> {

	/**
	* 主键
	*/
	@TableId(type = IdType.AUTO)
	@Schema(description="主键")
   private Long id;
	/**
	* 服务商利润id
	*/
	@Schema(description="服务商利润id")
   private Long profitId;
	/**
	* 起始重量
	*/
	@Schema(description="起始重量")
   private BigDecimal startWeight;
	/**
	* 结束重量
	*/
	@Schema(description="结束重量")
   private BigDecimal endWeight;
	/**
	* 配置值
	*/
	@Schema(description="配置值")
   private BigDecimal configValue;

}
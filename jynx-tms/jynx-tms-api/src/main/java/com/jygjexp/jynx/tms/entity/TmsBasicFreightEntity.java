package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseEntity;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 基础运费表
 *
 * <AUTHOR>
 * @date 2025-03-06 10:48:05
 */
@Data
@TenantTable
@TableName("tms_basic_freight")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "基础运费表")
public class TmsBasicFreightEntity extends BaseLogicEntity<TmsBasicFreightEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 起点
	*/
    @Schema(description="起点")
    private String origin;

	/**
	* 终点
	*/
    @Schema(description="终点")
    private String destination;

	/**z
	* MIN 运费
	*/
    @Schema(description="MIN 运费")
    private BigDecimal min;

	/**
	* LTL 运费
	*/
    @Schema(description="LTL 运费")
    private BigDecimal ltl;

	/**
	* 利润率（百分比）
	*/
    @Schema(description="利润率（百分比）")
    private BigDecimal profitMargin;

	/**
	* CWT-1000 运费
	*/
    @Schema(description="CWT-1000 运费")
	@TableField("cwt_1000")
    private BigDecimal cwt1000;

	/**
	* CWT-2000 运费
	*/
    @Schema(description="CWT-2000 运费")
	@TableField("cwt_2000")
    private BigDecimal cwt2000;

	/**
	* CWT-5000 运费
	*/
    @Schema(description="CWT-5000 运费")
	@TableField("cwt_5000")
    private BigDecimal cwt5000;

	/**
	* CWT-10000 运费
	*/
    @Schema(description="CWT-10000 运费")
	@TableField("cwt_10000")
    private BigDecimal cwt10000;

}
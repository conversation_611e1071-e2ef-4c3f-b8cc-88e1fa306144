package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * TMS区域邮编分区
 *
 * <AUTHOR>
 * @date 2025-03-20 11:10:21
 */
@Data
@TenantTable
@TableName("tms_zip_zone")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "TMS区域邮编分区")
public class TmsZipZoneEntity extends BaseLogicEntity<TmsZipZoneEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

	/**
	* 区域id
	*/
    @Schema(description="区域id")
    private Long regionId;

	/**
	* 路线编号
	*/
    @Schema(description="路线编号")
    private String routeNumber;

	/**
	* 覆盖邮编
	*/
    @Schema(description="覆盖邮编")
    private String overlayPostcode;

	/**
	* 覆盖邮编数量
	*/
    @Schema(description="覆盖邮编数量")
    private Integer overlayNumber;

	/**
	 * 点位数据
	 */
	@Schema(description="点位数据")
	private String geometry;

}
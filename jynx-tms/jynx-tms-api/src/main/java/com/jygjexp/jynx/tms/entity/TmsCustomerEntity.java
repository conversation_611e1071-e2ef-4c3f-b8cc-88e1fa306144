package com.jygjexp.jynx.tms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jygjexp.jynx.common.core.util.TenantTable;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 卡派-客户信息表
 *
 * <AUTHOR>
 * @date 2025-02-24 15:46:47
 */
@Data
@TenantTable
@TableName("tms_customer")
@EqualsAndHashCode(callSuper = true)
@HeadFontStyle(fontHeightInPoints = 10) // 设置表头字体样式
@ExcelIgnoreUnannotated
@Schema(description = "卡派-客户信息表")
public class TmsCustomerEntity extends BaseLogicEntity<TmsCustomerEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 客户名称
	*/
	@ColumnWidth(15)
	@ExcelProperty("(Customer name)账户名称")
    @Schema(description="账户名称")
    private String customerName;

	/**
	 * 账户密码
	 */
	@Schema(description="账户密码")
	private String password;

	/**
	* 客户名称 中文
	*/
	@ColumnWidth(15)
	@ExcelProperty("(Customer name)公司名称")
    @Schema(description="公司名称")
    private String customerNameCn;

	/**
	* 客户编码
	*/
	@ColumnWidth(15)
	@ExcelProperty("(Customer code)客户编码")
    @Schema(description="客户编码")
    private String customerCode;

	/**
	* 所属行业
	*/
	@ColumnWidth(10)
	//@ExcelProperty("(Sector)所属行业")
    @Schema(description="所属行业")
    private String sector;

	/**
	* 客户级别
	*/
	@ColumnWidth(10)
	@ExcelProperty("(CustomerLevel)客户级别")
    @Schema(description="客户级别")
    private Integer customerLevel;

	/**
	* 客户标签
	*/
	@ColumnWidth(10)
	//@ExcelProperty("(CustomerLabel)客户标签")
    @Schema(description="客户标签")
    private String customerLabel;

	/**
	* 城市
	*/
	@ColumnWidth(10)
	@ExcelProperty("(City)城市")
    @Schema(description="城市")
    private String region;

	/**
	* 公司地址
	*/
	@ColumnWidth(15)
	@ExcelProperty("(CompanyAddress)公司地址")
    @Schema(description="公司地址")
    private String companyAddress;

	/**
	* 地址邮编
	*/
	@ColumnWidth(15)
	@ExcelProperty("(PostalCode)地址邮编")
    @Schema(description="地址邮编")
    private String postalCode;

	/**
	* 营业执照
	*/
	@ColumnWidth(20)
	@ExcelProperty("(BusinessLicense)营业执照")
    @Schema(description="营业执照")
    private String businessLicense;

	/**
	* 联系人姓名
	*/
	@ColumnWidth(15)
	@ExcelProperty("(ContactPerson)联系人姓名")
    @Schema(description="联系人姓名")
    private String contactPerson;

	/**
	* 手机号
	*/
	@ColumnWidth(15)
	@ExcelProperty("(Phone)手机号")
    @Schema(description="手机号")
    private String phone;

	/**
	* 部门
	*/
	@ColumnWidth(15)
	//@ExcelProperty("(Department)部门")
    @Schema(description="部门")
    private String department;

	/**
	* 职位
	*/
	@ColumnWidth(10)
	//@ExcelProperty("(Position)职位")
    @Schema(description="职位")
    private String position;

	/**
	* 邮箱
	*/
	@ColumnWidth(15)
	@ExcelProperty("(Email)公司邮箱")
    @Schema(description="公司邮箱")
    private String email;

	/**
	* 仓库地址
	*/
	@ColumnWidth(20)
	//@ExcelProperty("(WarehouseAddress)仓库地址")
    @Schema(description="仓库地址")
    private String warehouseAddress;

	/**
	 * 客户分类
	 */
	@ColumnWidth(10)
	@ExcelProperty("(customer classification)客户分类")
	@Schema(description="客户分类")
	private String customerCategory;


	/**
	 * 账单邮箱
	 */
	@ColumnWidth(10)
	@ExcelProperty("(Billing mailbox)账单邮箱")
	@Schema(description="账单邮箱")
	private String billingEmail;

	/**
	 * 报价邮箱
	 */
	@ColumnWidth(10)
	@ExcelProperty("(Quotation email)报价邮箱")
	@Schema(description="报价邮箱")
	private String quotationEmail;

	/**
	 * 身份证号码（客户类型为个人时才会有）
	 */
	@Schema(description = "身份证号码（客户类型为个人时才会有）")
	private String idCard;

	/**
	 * 身份证头像面图片
	 */
	@Schema(description = "身份证头像面图片")
	private String idCardFront;

	/**
	 * 身份证国徽面图片
	 */
	@Schema(description = "身份证国徽面图片")
	private String idCardBack;

	/**
	 * 单量
	 */
	@Schema(description = "单量")
	private Integer count;

	/**
	 * token
	 */
	@Schema(description = "token")
	@ExcelIgnore
	private String token;

	/**
	* 启用状态：0 禁用，1 启用
	*/
	@ColumnWidth(10)
	@ExcelProperty("(IsValid：0 Disable，1 Enable)启用状态：0 禁用，1 启用")
    @Schema(description="启用状态：0 禁用，1 启用")
    private Integer isValid;

	/**
	 * 客户对应的用户id
	 */
	@Schema(description = "客户对应的用户id")
	private Long userId;

	/**
	 * 是否推送其他服务商
	 */
	@Schema(description="推送其他服务商")
	private String isPush;

	/**
	 * 材积系数
	 */
	@Schema(description="材积系数")
	private Integer volumeCoefficient;

	/**
	 * 体积重开关 0 未开启 1开启
	 */
	@Schema(description = "体积重开关 0 未开启 1开启")
	private Boolean volumeWeightSwitch;

	/**
	 * webhook
	 */
	@Schema(description = "回调接口")
	private String webhook;



}
package com.jygjexp.jynx.tms.feign;


import com.jygjexp.jynx.admin.api.entity.SysDictItem;
import com.jygjexp.jynx.common.core.constant.SecurityConstants;
import com.jygjexp.jynx.common.core.constant.ServiceNameConstants;
import com.jygjexp.jynx.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "RemoteTmsService", value = ServiceNameConstants.UPMS_SERVICE)
public interface RemoteUpmsService {

    /**
     * 获取字典
     *
     * @return
     */
    @GetMapping("/dict/type/{type}")
    R<List<SysDictItem>> getDictByType(@PathVariable("type") String type,@RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 获取指定用户全部信息
     * @return 用户信息
     */
    @GetMapping("/user/info/{username}")
    R info(@PathVariable String username,@RequestHeader(SecurityConstants.FROM) String from) ;


    /**
     * 获取商家（退件-来源渠道）字典数据
     */
    @GetMapping("/dict/getMerchantDict")
    List<SysDictItem> getMerchantDict(@RequestParam String query);

    /**
     * 获取格口字典数据
     */
    @GetMapping("/dict/getGridDict")
    List<SysDictItem> getGridDict( @RequestParam String query);

    }

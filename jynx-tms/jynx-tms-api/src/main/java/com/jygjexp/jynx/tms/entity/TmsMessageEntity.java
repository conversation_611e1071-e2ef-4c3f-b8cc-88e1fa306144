package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * TMS消息
 *
 * <AUTHOR>
 * @date 2025-03-05 10:02:20
 */
@Data
@TenantTable
@TableName("tms_message")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "TMS消息")
public class TmsMessageEntity extends Model<TmsMessageEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Integer id;

	/**
	* 用户ID
	*/
    @Schema(description="用户ID")
    private Long userId;

	/**
	 * 消息类型  1 异常订单 2 揽收订单 3 派送订单 4 干线订单
	 */
	@Schema(description="消息类型")
	private Integer messageType;

	/**
	* 订单ID
	*/
    @Schema(description="订单ID")
    private String orderNo;

	/**
	* 消息
	*/
    @Schema(description="消息")
    private String message;

	/**
	* 是否已读
	*/
    @Schema(description="是否已读")
    private Integer isRead;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 租户ID
	*/
    @Schema(description="租户ID")
    private Long tenantId;
}
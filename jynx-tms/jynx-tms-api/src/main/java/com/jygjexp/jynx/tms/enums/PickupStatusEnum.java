package com.jygjexp.jynx.tms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum PickupStatusEnum {

    WAIT_CONTACT(0, "待联系"),
    APPOINTMENT(1, "预约待取货"),
    PICKED_UP(2, "已取件"),
    REDELIVERY(3, "重新派送"),
    DESTROYED(4, "销毁状态");

    private final int code;
    private final String description;

    /**
     * 根据 code 获取枚举
     */
    public static PickupStatusEnum fromCode(int code) {
        for (PickupStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }
}

package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 干线任务单
 *
 * <AUTHOR>
 * @date 2025-04-07 18:36:05
 */
@Data
@TenantTable
@TableName("tms_line_haul_order")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "干线任务单")
public class TmsLineHaulOrderEntity extends BaseLogicEntity<TmsLineHaulOrderEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 干线单号
	*/
    @Schema(description="干线单号")
    private String lineHaulNo;

	/**
	* 运输类型：1=整车运输，2=零担运输
	*/
    @Schema(description="运输类型：1=整车运输，2=零担运输")
    private Integer transportType;

	/**
	* 任务状态（25001-待提货，25002-配送中，25003-已完成，25004-已取消）
	*/
    @Schema(description="任务状态（25001-待提货，25002-配送中，25003-已完成，25004-已取消）")
    private Integer taskStatus;

	/**
	* 司机ID，关联司机表
	*/
    @Schema(description="司机ID，关联司机表")
    private Long driverId;

	/**
	* 司机联系方式
	*/
    @Schema(description="司机联系方式")
    private String contactPhone;

	/**
	* 车牌号
	*/
    @Schema(description="车牌号")
    private String licensePlate;

	/**
	* 始发地仓库ID
	*/
    @Schema(description="始发地仓库ID")
    private Long originWarehouseId;

	/**
	* 目的地仓库ID
	*/
    @Schema(description="目的地仓库ID")
    private Long destWarehouseId;

	/**
	* 计划出发开始时间
	*/
    @Schema(description="计划出发开始时间")
    private LocalDateTime plannedDepartureStartTime;

	/**
	 * 计划出发结束时间
	 */
	@Schema(description="计划出发结束时间")
	private LocalDateTime plannedDepartureEndTime;

	/**
	 * 订单数量
	 */
	@Schema(description="订单数量")
	private Integer orderCount;
	/**
	* 货物数量
	*/
    @Schema(description="货物数量")
    private Integer totalQuantity;

	/**
	* 总体积(m³)
	*/
    @Schema(description="总体积(m³)")
    private BigDecimal totalVolume;

	/**
	* 总重量(kg)
	*/
    @Schema(description="总重量(kg)")
    private BigDecimal totalWeight;

	/**
	 * 提货证明（最多6张图片，逗号分割）
	 */
	@Schema(description="提货证明（最多6张图片，逗号分割）")
	private String pickupProof;

	/**
	 * 送货证明（最多6张图片，逗号分割）
	 */
	@Schema(description="送货证明（最多6张图片，逗号分割）")
	private String deliveryProof;

	/**
	 * 提货时间
	 */
	@Schema(description="提货时间")
	private LocalDateTime pickupTime;

	/**
	 * 送货时间
	 */
	@Schema(description="送货时间")
	private LocalDateTime deliveryTime;

	/**
	 * 是否扫描：0：未扫描/1:已扫描
	 */
	@Schema(description="是否扫描：0：未扫描/1:已扫描")
	private Boolean isScan;

	/**
	 * 扫描时间
	 */
	@Schema(description="扫描时间")
	private LocalDateTime scanTime;

}
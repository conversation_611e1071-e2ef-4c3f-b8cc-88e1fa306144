package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 中大件-司机信息
 *
 * <AUTHOR>
 * @date 2025-04-02 20:15:09
 */
@Data
@TenantTable
@TableName("tms_lmd_driver")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "中大件-司机信息")
public class TmsLmdDriverEntity extends BaseLogicEntity<TmsLmdDriverEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Long driverId;

	/**
	* 姓名
	*/
    @Schema(description="姓名")
    private String driverName;

	/**
	* 手机号码
	*/
    @Schema(description="手机号码")
    private String phone;

	/**
	* 驾照号
	*/
    @Schema(description="驾照号")
    private String idNumber;

	/**
	* 国家
	*/
    @Schema(description="国家")
    private String country;

	/**
	* 省份
	*/
    @Schema(description="省份")
    private String province;

	/**
	* 区域id
	*/
    @Schema(description="区域id")
    private String regionId;

	/**
	* 邮编
	*/
    @Schema(description="邮编")
    private String zip;

	/**
	* 邮箱
	*/
    @Schema(description="邮箱")
    private String email;

	/**
	* 社保号码
	*/
    @Schema(description="社保号码")
    private String socialSecurityNumber;

	/**
	* 家庭住址
	*/
    @Schema(description="家庭住址")
    private String homeAddress;

	/**
	* 紧急联系人
	*/
    @Schema(description="紧急联系人")
    private String emergencyName;

	/**
	* 紧急联系人电话
	*/
    @Schema(description="紧急联系人电话")
    private String emergencyPhone;

	/**
	* 所属承运商
	*/
    @Schema(description="所属承运商")
    private Long carrierId;

	/**
	* 驾照类型
	*/
    @Schema(description="驾照类型")
    private String licenseType;

	/**
	* 驾照有效期开始
	*/
    @Schema(description="驾照有效期开始")
    private LocalDate licenseTimeStart;

	/**
	* 驾照有效期结束
	*/
    @Schema(description="驾照有效期结束")
    private LocalDate licenseTimeEnd;

	/**
	* 工作方式 0:全职，1：兼职，2：外包
	*/
    @Schema(description="工作方式 0:全职，1：兼职，2：外包")
    private Integer workType;

	/**
	* 司机类型 0：干线，1：卡派，2：中大件
	*/
    @Schema(description="司机类型 0：干线，1：卡派，2：中大件")
    private String driverType;

	/**
	* 机构编号
	*/
    @Schema(description="机构编号")
    private String orgCode;

	/**
	* 运输编号
	*/
    @Schema(description="运输编号")
    private String transportNumber;

	/**
	* 银行账号
	*/
    @Schema(description="银行账号")
    private String bankAccount;

	/**
	* 是否营业，0：休息，1：已营业
	*/
    @Schema(description="是否营业，0：休息，1：已营业")
    private Boolean isOpen;

	/**
	* 证件照正面图片
	*/
    @Schema(description="证件照正面图片")
    private String idCardFront;

	/**
	* 证件照反面图片
	*/
    @Schema(description="证件照反面图片")
    private String idCardBack;

	/**
	* 驾驶证正面图片
	*/
    @Schema(description="驾驶证正面图片")
    private String drivingLicenseFront;

	/**
	* 驾驶证反面图片
	*/
    @Schema(description="驾驶证反面图片")
    private String drivingLicenseBack;

	/**
	* 其他资质证明文件
	*/
    @Schema(description="其他资质证明文件")
    private String otherQualification;


	/**
	 * 审核状态：0：未审核、1：已驳回、2：已审核
	 */
	@Schema(description="审核状态：0：未审核、1：已驳回、2：已审核")
	private Integer auditStatus;

	/**
	 * 计费模式：0：时薪机制、1：时薪保底机制、2：计件机制
	 */
	@Schema(description="计费模式")
	private Integer billingModel;

	// 审核时间
	@Schema(description="审核时间")
	private LocalDateTime auditTime;

	/**
	 * 司机号
	 */
	@Schema(description="司机号")
	private String driverNum;

	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;

}
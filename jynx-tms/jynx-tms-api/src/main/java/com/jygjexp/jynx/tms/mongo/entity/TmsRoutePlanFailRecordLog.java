package com.jygjexp.jynx.tms.mongo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoplus.annotation.ID;
import com.mongoplus.annotation.collection.CollectionName;
import com.mongoplus.enums.IdTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "路径规划失败记录")
@CollectionName("route_plan_fail_record_log")
public class TmsRoutePlanFailRecordLog {

    /**
     * id
     */
    @ID(type = IdTypeEnum.ASSIGN_ID)
    @Schema(description="id")
    private Long id;

    /**
     * 路径规划失败key（司机id）
     */
    @Schema(description="路径规划失败key（司机id）")
    private String failKey;

    /**
     * 失败订单的日期
     */
    @Schema(description="失败订单的日期")
    private LocalDate date;

    /**
     * 失败订单
     */
    @Schema(description="失败订单")
    private String failOrder;

    /**
     * 失败原因
     */
    @Schema(description = "失败原因")
    private String failReason;

    /**
     * 是否处理（0：未处理，1：处理）
     */
    @Schema(description="是否处理（0：未处理，1：处理）")
    private Integer isHandle;

    /**
     * 失败类型
     */
    @Schema(description="失败类型")
    private String type;

    /**
     * 失败请求的请求参数Json
     */
    @Schema(description="失败类型")
    private String paramJson;

    /**
     * 状态
     */
    @Schema(description="状态")
    private Integer status;

    /**
     * 站点id
     */
    @Schema(description="站点id")
    private Integer siteId;

    /**
     * 乐观锁
     */
    @Schema(description="乐观锁")
    private Integer revision;

    /**
     * 备注
     */
    @Schema(description="备注")
    private String remark;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 逻辑删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="逻辑删除")
    private String delFlag;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Integer tenantId;
}

package com.jygjexp.jynx.tms.utils;

import java.time.LocalDate;

public class ExpressOrderNoUtil {

    // 月份编码：1~9->1~9, 10->A, 11->B, 12->C
    public static String encodeMonth(int month) {
        if (month >= 1 && month <= 9) {
            return String.valueOf(month);
        } else if (month == 10) {
            return "A";
        } else if (month == 11) {
            return "B";
        } else if (month == 12) {
            return "C";
        } else {
            throw new IllegalArgumentException("月份非法: " + month);
        }
    }

    // 日编码：1~9->1~9, 10->A, 11->B, ..., 31->V
    public static String encodeDay(int day) {
        if (day >= 1 && day <= 9) {
            return String.valueOf(day);
        } else if (day >= 10 && day <= 31) {
            char code = (char) ('A' + (day - 10));
            return String.valueOf(code);
        } else {
            throw new IllegalArgumentException("日期非法: " + day);
        }
    }

    // 生成主单号（自动取当前年月日）
    public static String generateMainOrderNo(int orderCount) {
        LocalDate now = LocalDate.now();
        String year = String.valueOf(now.getYear()).substring(2, 4);
        String monthStr = encodeMonth(now.getMonthValue());
        String dayStr = encodeDay(now.getDayOfMonth());
        String orderCountStr = String.format("%07d", orderCount);
        return "N" + year + monthStr + dayStr + "E" + orderCountStr;
    }

    /**
     * 生成子单号
     * @param mainOrderNo 主单号
     * @param index 子单顺序（从1开始）
     * @return 子单号
     */
    public static String generateSubOrderNo(String mainOrderNo, int index) {
        return mainOrderNo + String.format("%03d", index);
    }
}

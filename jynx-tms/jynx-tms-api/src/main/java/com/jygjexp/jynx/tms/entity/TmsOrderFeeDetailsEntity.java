package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单费用明细表
 *
 * <AUTHOR>
 * @date 2025-09-01 16:27:11
 */
@Data
@TableName("tms_order_fee_details")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订单费用明细表")
public class TmsOrderFeeDetailsEntity extends Model<TmsOrderFeeDetailsEntity> {

 
	/**
	* id
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="id")
    private Long id;

	/**
	* 订单号
	*/
    @Schema(description="订单号")
    private String orderNo;

	/**
	* 预报基础运费(CAD)
	*/
    @Schema(description="预报基础运费(CAD)")
    private BigDecimal forecastBaseFreight;

	/**
	* 预报税费(CAD)
	*/
    @Schema(description="预报税费(CAD)")
    private BigDecimal forecastTax;

	/**
	* 预报附加费(CAD)
	*/
    @Schema(description="预报附加费(CAD)")
    private BigDecimal forecastAdditionalFee;

	/**
	* 预报总费用(CAD)
	*/
    @Schema(description="预报总费用(CAD)")
    private BigDecimal forecastTotalFee;

	/**
	 * 预报附加费明细ID，逗号分隔
	 */
	@Schema(description="预报附加费明细ID，逗号分隔")
	private String forecastSurchargeDetailIds;

	/**
	* 实际基础运费(CAD)
	*/
    @Schema(description="实际基础运费(CAD)")
    private BigDecimal actualBaseFreight;

	/**
	* 实际税费(CAD)
	*/
    @Schema(description="实际税费(CAD)")
    private BigDecimal actualTax;

	/**
	* 实际附加费(CAD)
	*/
    @Schema(description="实际附加费(CAD)")
    private BigDecimal actualAdditionalFee;

	/**
	* 实际总费用(CAD)
	*/
    @Schema(description="实际总费用(CAD)")
    private BigDecimal actualTotalFee;

	/**
	 * 实际附加费明细ID，逗号分隔
	 */
	@Schema(description="预报附加费明细ID，逗号分隔")
	private String  actualSurchargeDetailIds;


	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;
}
package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 门店客户信用卡表
 *
 * <AUTHOR>
 * @date 2025-07-14 17:49:17
 */
@Data
@TenantTable
@TableName("tms_store_credit_card")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门店客户信用卡表")
public class TmsStoreCreditCardEntity extends Model<TmsStoreCreditCardEntity> {


    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private Long id;

    /**
     * 客户id
     */
    @Schema(description="客户id")
    private Long storeCustomerId;

    /**
     * 卡号
     */
    @Schema(description="卡号")
    private String cardNumber;

    /**
     * 有效日期
     */
    @Schema(description="有效日期")
    private LocalDateTime expiryDate;

    /**
     * cvc
     */
    @Schema(description="cvc")
    private String cvc;

    /**
     * 持卡人
     */
    @Schema(description="持卡人")
    private String holderName;

    /**
     * 是否默认:0否 1是
     */
    @Schema(description="是否默认:0否 1是")
    private Integer defaultFlag;

    /**
     * 乐观锁
     */
    @Schema(description="乐观锁")
    private Integer revision;

    /**
     * 备注
     */
    @Schema(description="备注")
    private String remark;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记：0未删除，1已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标记：0未删除，1已删除")
    private String delFlag;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;
}

package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 订单日志
 *
 * <AUTHOR>
 * @date 2025-06-13 09:38:59
 */
@Data
@TableName("tms_order_log")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订单日志")
public class TmsOrderLogEntity extends Model<TmsOrderLogEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

	/**
	 * 订单号
	 */
	@Schema(description="订单号")
	private String orderNo;


	/**
	* 日志类型
	*/
    @Schema(description="日志类型")
    private Integer type;

	/**
	* 详情
	*/
    @Schema(description="详情")
    private String detail;

	/**
	* 操作人
	*/
    @Schema(description="操作人")
    private String opertion;

	/**
	* 日期
	*/
    @Schema(description="日期")
    private LocalDateTime time;
}
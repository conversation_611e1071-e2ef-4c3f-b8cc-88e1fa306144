package com.jygjexp.jynx.tms.utils;

import java.util.HashMap;
import java.util.Map;

public class UniStatusMap {
    private static final Map<String, Integer> UNI_TO_LOCAL_STATUS = new HashMap<>();
    
    static {
        UNI_TO_LOCAL_STATUS.put("200", 3);  // "待运输"
        UNI_TO_LOCAL_STATUS.put("315", 4);  // "运输中"
        UNI_TO_LOCAL_STATUS.put("312", 5);  // "待收货"
        UNI_TO_LOCAL_STATUS.put("520", 6);  // "已完成"
    }
    
    public static Integer mapToLocalStatus(String uniStatus) {
        return UNI_TO_LOCAL_STATUS.getOrDefault(uniStatus, 4);
    }
}
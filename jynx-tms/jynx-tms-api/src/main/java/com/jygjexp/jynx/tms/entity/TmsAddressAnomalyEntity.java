package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 地址异常记录
 *
 * <AUTHOR>
 * @date 2025-06-12 18:03:40
 */
@Data
@TableName("tms_address_anomaly")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "地址异常记录")
public class TmsAddressAnomalyEntity extends Model<TmsAddressAnomalyEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

	/**
	* 地址
	*/
    @Schema(description="地址")
    private String address;

	/**
	* 邮编
	*/
    @Schema(description="邮编")
    private String zip;

	/**
	* 省份
	*/
    @Schema(description="省份")
    private String province;

	/**
	* 地址异常类型 0公寓 1配送失败 2赔偿 3投诉 4丢件
	*/
    @Schema(description="地址异常类型 0公寓 1配送失败 2赔偿 3投诉 4丢件")
    private Integer type;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;
}
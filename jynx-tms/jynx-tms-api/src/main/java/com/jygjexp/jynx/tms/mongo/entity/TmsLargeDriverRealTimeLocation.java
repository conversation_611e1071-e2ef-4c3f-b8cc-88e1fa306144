package com.jygjexp.jynx.tms.mongo.entity;

import com.mongoplus.annotation.ID;
import com.mongoplus.annotation.collection.CollectionName;
import com.mongoplus.enums.IdTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "中大件司机实时经纬度上传")
@CollectionName("large_driver_real_time_location")
public class TmsLargeDriverRealTimeLocation {
    /**
     * id
     */
    @ID(type = IdTypeEnum.ASSIGN_ID)
    @Schema(description = "id")
    private Long id;
    /**
     * 司机id
     */
    @Schema(description = "司机id")
    private Long driverId;
    /**
     * 记录任务类型（1；揽收，2：干线，3：派送）
     */
    @Schema(description = "记录任务类型（1；揽收，2：干线，3：派送）")
    private Integer taskType;
    /**
     * 任务单号
     */
    @Schema(description = "任务单号")
    private String taskOrderNo;
    /**
     * 纬度
     */
    @Schema(description = "纬度")
    private BigDecimal lat;
    /**
     * 经度
     */
    @Schema(description = "经度")
    private BigDecimal lng;
    /**
     * 上传时间
     */
    @Schema(description = "上传时间")
    private LocalDateTime uploadTime;

}

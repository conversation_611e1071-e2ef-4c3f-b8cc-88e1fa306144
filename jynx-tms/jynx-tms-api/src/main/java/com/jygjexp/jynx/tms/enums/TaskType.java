package com.jygjexp.jynx.tms.enums;

import lombok.Getter;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum TaskType {

    /**
     * 揽收任务
     */
    COLLECTION(1, "COLLECTION"),

    /**
     * 派送任务
     */
    DELIVERY(2, "DELIVERY"),

    /**
     * 干线任务
     */
    DRYLINE(3, "DRYLINE");

    private final Integer code;
    private final String value;

    TaskType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    // 循环变量
    private static final Map<Integer, TaskType> LOOKUP = new HashMap<>();

    // 静态初始化
    static {
        for (TaskType taskType : EnumSet.allOf(TaskType.class)) {
            LOOKUP.put(taskType.code, taskType);
        }
    }

    /**
     * 根据code获取枚举项
     *
     * @param code
     * @return
     */
    public static TaskType lookup(Integer code) {
        return LOOKUP.get(code);
    }

}

package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jygjexp.jynx.common.core.util.TenantTable;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 司机信息记录
 *
 * <AUTHOR>
 * @date 2025-02-21 16:14:03
 */
@Data
@TenantTable
@TableName("tms_driver")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "司机信息记录")
public class TmsDriverEntity extends BaseLogicEntity<TmsDriverEntity> {


	/**
	* 司机信息主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="司机信息主键")
    private Long driverId;

	/**
	* 姓名
	*/
    @Schema(description="姓名")
    private String driverName;

	/**
	* 手机号码
	*/
    @Schema(description="手机号码")
    private String phone;

	/**
	* 身份证号
	*/
    @Schema(description="身份证号")
    private String idNumber;

	/**
	* 性别 0:男,1:女
	*/
    @Schema(description="性别 0:男,1:女")
    private Integer sex;

	/**
	* 驾照类型
	*/
    @Schema(description="驾照类型")
    private String licenseType;

	/**
	* 驾照有效期开始
	*/
    @Schema(description="驾照有效期开始")
    private LocalDate licenseTimeStart;

	/**
	 * 驾照有效期结束
	 */
	@Schema(description="驾照有效期结束")
	private LocalDate licenseTimeEnd;

	/**
	* 所属承运商
	*/
    @Schema(description="所属承运商")
    private Long carrierId;

	/**
	* 家庭住址
	*/
    @Schema(description="家庭住址")
    private String homeAddress;

	/**
	* 紧急联系人
	*/
    @Schema(description="紧急联系人")
    private String emergencyName;

	/**
	* 紧急联系人电话
	*/
    @Schema(description="紧急联系人电话")
    private String emergencyPhone;

	/**
	 * 是否营业
	 */
	@Schema(description="是否营业 0：休息，1：已营业")
	private Boolean isOpen;

	/**
	* 证件照正面图片
	*/
    @Schema(description="证件照正面图片")
    private String idCardFront;

	/**
	* 证件照反面图片
	*/
    @Schema(description="证件照反面图片")
    private String idCardBack;

	/**
	* 驾驶证正面图片
	*/
    @Schema(description="驾驶证正面图片")
    private String drivingLicenseFront;

	/**
	* 驾驶证反面图片
	*/
    @Schema(description="驾驶证反面图片")
    private String drivingLicenseBack;

	/**
	* 其他资质证明文件
	*/
    @Schema(description="其他资质证明文件")
    private String otherQualification;

	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;

}
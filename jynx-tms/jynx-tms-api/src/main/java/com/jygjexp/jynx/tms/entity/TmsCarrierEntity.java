package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jygjexp.jynx.common.core.util.TenantTable;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 承运商信息记录
 *
 * <AUTHOR>
 * @date 2025-02-21 16:13:23
 */
@Data
@TenantTable
@TableName("tms_carrier")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "承运商信息记录")
public class TmsCarrierEntity extends BaseLogicEntity<TmsCarrierEntity> {


	/**
	* 承运商信息主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="承运商信息主键")
    private Long carrierId;

	/**
	* 承运商编码
	*/
    @Schema(description="承运商编码")
    private String carrierCode;

	/**
	* 承运商名称
	*/
    @Schema(description="承运商名称")
    private String carrierName;

	/**
	* 承运商类型。1：自营、2：外包
	*/
    @Schema(description="承运商类型。1：自营、2：外包")
    private Integer carrierType;

	/**
	* 商业号码
	*/
    @Schema(description="商业号码")
    private String businessNumber;

	/**
	* 税务登记号码
	*/
    @Schema(description="税务登记号码")
    private String taxRegistrationNumber;

	/**
	* 法人代表
	*/
    @Schema(description="法人代表")
    private String legalName;

	/**
	* 城市
	*/
    @Schema(description="城市")
    private String region;

	/**
	* 公司地址
	*/
    @Schema(description="公司地址")
    private String companyAddress;

	/**
	* 邮编
	*/
    @Schema(description="邮编")
    private String postalCode;

	/**
	* 营业执照图片
	*/
    @Schema(description="营业执照图片")
    private String businessLicenseImage;

	/**
	* 联系人姓名
	*/
    @Schema(description="联系人姓名")
    private String name;

	/**
	* 手机号
	*/
    @Schema(description="手机号")
    private String phone;

	/**
	* 部门名称
	*/
    @Schema(description="部门名称")
    private String dept;

	/**
	* 职位
	*/
    @Schema(description="职位")
    private String position;

	/**
	* 邮箱
	*/
    @Schema(description="邮箱")
    private String carrierEmail;

	/**
	* 发票抬头
	*/
    @Schema(description="发票抬头")
    private String invoiceTitle;

	/**
	* 税号
	*/
    @Schema(description="税号")
    private String taxNumber;

	/**
	* 开户行
	*/
    @Schema(description="开户行")
    private String bankDeposit;

	/**
	* 开户名称
	*/
    @Schema(description="开户名称")
    private String bankName;

	/**
	* 银行账号
	*/
    @Schema(description="银行账号")
    private String bankNumber;

	/**
	* 是否签订合同 0:未签订,1:已签订
	*/
    @Schema(description="是否签订合同 0:未签订,1:已签订")
    private Integer isSignedCooperation;

	/**
	* 合同副本
	*/
    @Schema(description="合同副本")
    private String contractCopy;

	/**
	 * 工作时间开始
	 */
	@Schema(description="工作时间开始")
	private LocalTime openingTime ;

	/**
	 * 工作时间结束
	 */
	@Schema(description="工作时间结束")
	private LocalTime  closingTime;

	/**
	 * 支付方式 1.Email 2.银行 3.Cheque 4.Others
	 */
	@Schema(description="支付方式 1.Email 2.银行 3.Cheque 4.Others")
	private Integer payMethod;

	/**
	 * 支付账户信息
	 */
	@Schema(description="支付账户信息")
	private String payAccount;

	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;

}
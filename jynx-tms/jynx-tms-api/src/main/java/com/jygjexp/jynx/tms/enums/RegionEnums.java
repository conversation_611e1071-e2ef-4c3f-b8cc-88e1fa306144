package com.jygjexp.jynx.tms.enums;

import cn.hutool.core.util.ObjUtil;
import lombok.Getter;

@Getter
public enum RegionEnums {

    YYZ(0, "YYZ","Toronto Warehouse"),
    YVR(1, "YVR","Vancouver Warehouse"),
    ;

    private final int code;
    // 地区名称
    private final String region;
    // 口岸仓库
    private final String portWarehouse;

    RegionEnums(Integer code, String region, String portWarehouse) {
        this.code = code;
        this.region = region;
        this.portWarehouse = portWarehouse;
    }

    public static RegionEnums getInstance(Integer code) {
        if (code == null) {
            return null;
        }
        for (RegionEnums value : values()) {
            if (ObjUtil.equals(value.getCode(),code)) {
                return value;
            }
        }
        return null;
    }
}

package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 卡派邮编
 *
 * <AUTHOR>
 * @date 2025-02-28 16:35:00
 */
@Data
@TenantTable
@TableName("tms_postal_code")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卡派邮编")
public class TmsPostalCodeEntity extends BaseLogicEntity<TmsPostalCodeEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 邮政编码
	*/
    @Schema(description="邮政编码")
    private String postalCode;

	/**
	* 国家/地区
	*/
    @Schema(description="国家/地区")
    private String countryName;

	/**
	* 省/州
	*/
    @Schema(description="省/州")
    private String statesName;

	/**
	* 城市
	*/
    @Schema(description="城市")
    private String cityName;

	/**
	* 税率（百分比，如7.25表示7.25%）
	*/
    @Schema(description="税率")
    private BigDecimal taxRate;

	/**
	 * 纬度
	 */
	@Schema(description="纬度")
	private BigDecimal lat;

	/**
	 * 经度
	 */
	@Schema(description="经度")
	private BigDecimal lng;


	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;


}